#!/usr/bin/env python3
"""
Advanced Credit Card Web Scraper for StackEasy Project
Uses multiple scraping techniques including Selenium for JavaScript-heavy sites
"""

import pandas as pd
import requests
from bs4 import BeautifulSoup
import time
import json
import re
from urllib.parse import urlparse
import logging
from typing import Dict, List, Optional, Tuple
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedCreditCardScraper:
    def __init__(self, csv_file: str = 'credit_cards_validated.csv'):
        """Initialize the advanced scraper with multiple techniques"""
        self.csv_file = csv_file
        self.cards_df = pd.read_csv(csv_file)
        self.scraped_data = []
        
        # Headers for requests
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        
        # Session for requests
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # Selenium driver (will be initialized when needed)
        self.driver = None
        
    def setup_selenium_driver(self) -> bool:
        """Set up Selenium Chrome driver with optimal settings"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # Run in background
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument(f'--user-agent={self.headers["User-Agent"]}')
            
            # Disable images and CSS for faster loading
            prefs = {
                "profile.managed_default_content_settings.images": 2,
                "profile.default_content_setting_values.notifications": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(30)
            logger.info("Selenium Chrome driver initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Selenium driver: {e}")
            return False
    
    def get_page_with_requests(self, url: str) -> Optional[BeautifulSoup]:
        """Get page content using requests (fast, but limited for JS sites)"""
        try:
            logger.info(f"Fetching with requests: {url}")
            response = self.session.get(url, timeout=15)
            response.raise_for_status()
            return BeautifulSoup(response.content, 'html.parser')
        except Exception as e:
            logger.warning(f"Requests failed for {url}: {e}")
            return None
    
    def get_page_with_selenium(self, url: str, wait_for_content: bool = True) -> Optional[BeautifulSoup]:
        """Get page content using Selenium (slower, but handles JS)"""
        try:
            if not self.driver:
                if not self.setup_selenium_driver():
                    return None
            
            logger.info(f"Fetching with Selenium: {url}")
            self.driver.get(url)
            
            if wait_for_content:
                # Wait for content to load
                try:
                    # Wait for common elements that indicate page is loaded
                    wait = WebDriverWait(self.driver, 10)
                    wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                    
                    # Additional wait for dynamic content
                    time.sleep(3)
                    
                except TimeoutException:
                    logger.warning(f"Timeout waiting for content on {url}")
            
            # Get page source and parse
            page_source = self.driver.page_source
            return BeautifulSoup(page_source, 'html.parser')
            
        except Exception as e:
            logger.error(f"Selenium failed for {url}: {e}")
            return None
    
    def get_page_content(self, url: str, use_selenium: bool = False) -> Tuple[Optional[BeautifulSoup], str]:
        """Get page content with fallback strategy"""
        method_used = "none"
        
        if not use_selenium:
            # Try requests first (faster)
            soup = self.get_page_with_requests(url)
            if soup:
                method_used = "requests"
                # Check if we got meaningful content
                text_length = len(soup.get_text().strip())
                if text_length > 100:  # Reasonable amount of content
                    return soup, method_used
                else:
                    logger.info(f"Requests got minimal content ({text_length} chars), trying Selenium")
        
        # Fallback to Selenium
        soup = self.get_page_with_selenium(url)
        if soup:
            method_used = "selenium"
            return soup, method_used
        
        return None, method_used
    
    def extract_enhanced_features(self, soup: BeautifulSoup, url: str, method: str) -> Dict:
        """Enhanced feature extraction with improved patterns"""
        features = {
            'annual_fee': None,
            'rewards_rate': {},
            'signup_bonus': None,
            'apr': None,
            'benefits': [],
            'categories': [],
            'scraping_method': method,
            'meta_description': None,
            'page_title': None
        }
        
        try:
            # Extract meta information
            meta_desc = soup.find('meta', {'property': 'og:description'}) or soup.find('meta', {'name': 'description'})
            if meta_desc:
                features['meta_description'] = meta_desc.get('content', '')
            
            title_tag = soup.find('title')
            if title_tag:
                features['page_title'] = title_tag.get_text().strip()
            
            # Get all text for analysis
            page_text = soup.get_text()
            search_text = page_text.lower()
            
            # Include meta description in search
            if features['meta_description']:
                search_text += " " + features['meta_description'].lower()
            
            logger.info(f"Analyzing {len(page_text)} characters of content")
            
            # Enhanced annual fee patterns
            self._extract_annual_fee(search_text, features)
            
            # Enhanced rewards patterns
            self._extract_rewards(search_text, features)
            
            # Enhanced signup bonus patterns
            self._extract_signup_bonus(search_text, features)
            
            # Extract APR information
            self._extract_apr(search_text, features)
            
        except Exception as e:
            logger.error(f"Error in enhanced extraction for {url}: {e}")
        
        return features
    
    def _extract_annual_fee(self, text: str, features: Dict):
        """Extract annual fee with comprehensive patterns"""
        # No fee patterns
        no_fee_patterns = [
            r'no\s*annual\s*fee',
            r'\$0\s*annual\s*fee',
            r'zero\s*annual\s*fee',
            r'waived\s*annual\s*fee',
            r'annual\s*fee\s*waived',
            r'no\s*fee\s*for\s*the\s*first\s*year'
        ]
        
        for pattern in no_fee_patterns:
            if re.search(pattern, text):
                features['annual_fee'] = "$0"
                logger.info("Found no annual fee")
                return
        
        # Fee amount patterns
        fee_patterns = [
            r'\$(\d+)\s*annual\s*fee',
            r'annual\s*fee[:\s]*\$(\d+)',
            r'\$(\d+)\s*per\s*year',
            r'(\d+)\s*dollar\s*annual\s*fee',
            r'fee:\s*\$(\d+)',
            r'annual\s*membership\s*fee[:\s]*\$(\d+)',
            r'yearly\s*fee[:\s]*\$(\d+)'
        ]
        
        for pattern in fee_patterns:
            match = re.search(pattern, text)
            if match:
                features['annual_fee'] = f"${match.group(1)}"
                logger.info(f"Found annual fee: ${match.group(1)}")
                return
    
    def _extract_rewards(self, text: str, features: Dict):
        """Extract rewards with enhanced patterns"""
        rewards_patterns = [
            # Amex-style patterns
            r'earn\s*(\d+)x\s*(?:membership\s*rewards®?\s*)?points?\s*on\s*([^.!?,\n]+)',
            r'(\d+)x\s*(?:membership\s*rewards®?\s*)?points?\s*on\s*([^.!?,\n]+)',
            r'(\d+)x\s*points?\s*(?:on|for)\s*([^.!?,\n]+)',
            
            # Cash back patterns
            r'(\d+)%\s*cash\s*back\s*(?:on|for)\s*([^.!?,\n]+)',
            r'earn\s*(\d+)%\s*back\s*(?:on|for)\s*([^.!?,\n]+)',
            
            # Miles patterns
            r'(\d+)x\s*miles?\s*(?:on|for)\s*([^.!?,\n]+)',
            r'earn\s*(\d+)\s*miles?\s*per\s*\$1\s*(?:on|for)\s*([^.!?,\n]+)',
            
            # General points patterns
            r'(\d+)\s*points?\s*per\s*\$1\s*(?:on|for|spent\s*on)\s*([^.!?,\n]+)',
            r'earn\s*(\d+)\s*points?\s*for\s*every\s*\$1\s*spent\s*(?:on|at)\s*([^.!?,\n]+)'
        ]
        
        for pattern in rewards_patterns:
            matches = re.finditer(pattern, text)
            for match in matches:
                rate = match.group(1)
                category = match.group(2).strip()
                
                # Clean up category
                category = re.sub(r'\s+', ' ', category)
                category = re.sub(r'[®™]', '', category)
                category = category.strip()[:60]
                
                if len(category) > 3 and not re.search(r'^\d+$', category):
                    if 'cash' in pattern or '%' in pattern:
                        reward_type = f"{rate}%"
                    elif 'miles' in pattern:
                        reward_type = f"{rate}x miles"
                    else:
                        reward_type = f"{rate}x"
                    
                    features['rewards_rate'][category] = reward_type
                    logger.info(f"Found reward: {reward_type} for {category}")
    
    def _extract_signup_bonus(self, text: str, features: Dict):
        """Extract signup bonus with comprehensive patterns"""
        bonus_patterns = [
            r'(\d+,?\d*)\s*(?:bonus\s*)?(?:membership\s*rewards®?\s*)?points?\s*after\s*(?:you\s*)?spend\s*\$(\d+,?\d*)',
            r'earn\s*(\d+,?\d*)\s*(?:bonus\s*)?points?\s*when\s*you\s*spend\s*\$(\d+,?\d*)',
            r'\$(\d+,?\d*)\s*statement\s*credit\s*after\s*(?:you\s*)?spend\s*\$(\d+,?\d*)',
            r'welcome\s*(?:offer|bonus)[:\s]*(\d+,?\d*)\s*points?\s*after\s*spending\s*\$(\d+,?\d*)',
            r'get\s*(\d+,?\d*)\s*points?\s*after\s*spending\s*\$(\d+,?\d*)',
            r'(\d+,?\d*)\s*miles?\s*after\s*(?:you\s*)?spend\s*\$(\d+,?\d*)',
            r'\$(\d+,?\d*)\s*cash\s*back\s*after\s*(?:you\s*)?spend\s*\$(\d+,?\d*)'
        ]
        
        for pattern in bonus_patterns:
            match = re.search(pattern, text)
            if match:
                amount = match.group(1)
                spend = match.group(2)
                
                if 'statement.*credit' in pattern or 'cash.*back' in pattern:
                    features['signup_bonus'] = f"${amount} after ${spend} spend"
                elif 'miles' in pattern:
                    features['signup_bonus'] = f"{amount} miles after ${spend} spend"
                else:
                    features['signup_bonus'] = f"{amount} points after ${spend} spend"
                
                logger.info(f"Found signup bonus: {features['signup_bonus']}")
                return
    
    def _extract_apr(self, text: str, features: Dict):
        """Extract APR information"""
        apr_patterns = [
            r'(\d+\.?\d*)%\s*(?:to\s*(\d+\.?\d*)%)?\s*(?:variable\s*)?apr',
            r'apr[:\s]*(\d+\.?\d*)%\s*(?:to\s*(\d+\.?\d*)%)?',
            r'interest\s*rate[:\s]*(\d+\.?\d*)%\s*(?:to\s*(\d+\.?\d*)%)?'
        ]
        
        for pattern in apr_patterns:
            match = re.search(pattern, text)
            if match:
                low_apr = match.group(1)
                high_apr = match.group(2) if match.group(2) else None
                
                if high_apr:
                    features['apr'] = f"{low_apr}% - {high_apr}%"
                else:
                    features['apr'] = f"{low_apr}%"
                
                logger.info(f"Found APR: {features['apr']}")
                return
    
    def scrape_card_advanced(self, issuer: str, card_name: str, url: str, force_selenium: bool = False) -> Optional[Dict]:
        """Scrape a single card with advanced techniques"""
        logger.info(f"Advanced scraping: {issuer} - {card_name}")
        
        soup, method = self.get_page_content(url, use_selenium=force_selenium)
        if not soup:
            logger.error(f"Failed to get content for {url}")
            return None
        
        features = self.extract_enhanced_features(soup, url, method)
        
        # Add basic card info
        card_data = {
            'issuer': issuer,
            'card_name': card_name,
            'url': url,
            'scraped_at': pd.Timestamp.now().isoformat(),
            **features
        }
        
        return card_data
    
    def test_advanced_scraping(self, num_cards: int = 3, use_selenium: bool = False):
        """Test advanced scraping on sample cards"""
        logger.info(f"Testing advanced scraping on {num_cards} cards (Selenium: {use_selenium})")
        
        sample_cards = self.cards_df.head(num_cards)
        results = []
        
        for i, (_, row) in enumerate(sample_cards.iterrows()):
            card_data = self.scrape_card_advanced(
                row['Issuer Name'], 
                row['Card Name'], 
                row['Card URL'],
                force_selenium=use_selenium
            )
            
            if card_data:
                results.append(card_data)
                self.scraped_data.append(card_data)
            
            # Be polite - wait between requests
            time.sleep(2)
        
        return results
    
    def save_results(self, filename: str = 'advanced_scraped_cards.json'):
        """Save results to JSON file"""
        with open(filename, 'w') as f:
            json.dump(self.scraped_data, f, indent=2)
        logger.info(f"Saved {len(self.scraped_data)} cards to {filename}")
    
    def cleanup(self):
        """Clean up resources"""
        if self.driver:
            self.driver.quit()
            logger.info("Selenium driver closed")

if __name__ == "__main__":
    scraper = AdvancedCreditCardScraper()
    
    try:
        print("🚀 Starting Advanced Credit Card Scraper...")
        print("📊 Testing with requests first, then Selenium...")
        
        # Test with requests first
        print("\n🔍 Phase 1: Testing with requests...")
        results_requests = scraper.test_advanced_scraping(3, use_selenium=False)
        
        print(f"\n✅ Phase 1 Results: {len(results_requests)} cards scraped")
        for card in results_requests:
            print(f"\n📋 {card['issuer']} - {card['card_name']}")
            print(f"   Method: {card.get('scraping_method', 'unknown')}")
            print(f"   Annual Fee: {card.get('annual_fee', 'Not found')}")
            print(f"   Rewards: {card.get('rewards_rate', {})}")
            print(f"   Signup Bonus: {card.get('signup_bonus', 'Not found')}")
        
        # Test with Selenium
        print("\n🤖 Phase 2: Testing with Selenium...")
        scraper.scraped_data = []  # Reset for clean comparison
        results_selenium = scraper.test_advanced_scraping(3, use_selenium=True)
        
        print(f"\n✅ Phase 2 Results: {len(results_selenium)} cards scraped")
        for card in results_selenium:
            print(f"\n📋 {card['issuer']} - {card['card_name']}")
            print(f"   Method: {card.get('scraping_method', 'unknown')}")
            print(f"   Annual Fee: {card.get('annual_fee', 'Not found')}")
            print(f"   Rewards: {card.get('rewards_rate', {})}")
            print(f"   Signup Bonus: {card.get('signup_bonus', 'Not found')}")
        
        # Save results
        scraper.save_results('advanced_test_results.json')
        print(f"\n💾 Results saved to advanced_test_results.json")
        
    except KeyboardInterrupt:
        print("\n⏹️ Scraping interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        scraper.cleanup()
