#!/usr/bin/env python3
"""
Test Bank of America Cards Scraping - Comprehensive Testing
Tests our advanced scraper on Bank of America credit cards
"""

import pandas as pd
from advanced_credit_card_scraper import AdvancedCreditCardScraper
import time
import json

def test_bank_of_america_cards():
    """Test scraper specifically on Bank of America cards"""
    
    # Load the CSV and filter for Bank of America cards
    df = pd.read_csv('credit_cards_validated.csv')
    bofa_cards = df[df['Issuer Name'] == 'Bank of America'].copy()
    
    print(f"🏦 Found {len(bofa_cards)} Bank of America cards to test")
    print("\n📋 Bank of America Cards List:")
    for i, (_, row) in enumerate(bofa_cards.iterrows(), 1):
        print(f"   {i}. {row['Card Name']}")
    
    # Initialize scraper
    scraper = AdvancedCreditCardScraper()
    
    # Test with first 5 Bank of America cards to get a good sample
    test_cards = bofa_cards.head(5)
    
    print(f"\n🧪 Testing with first 5 Bank of America cards...")
    print("=" * 70)
    
    results = []
    
    try:
        for i, (_, row) in enumerate(test_cards.iterrows(), 1):
            print(f"\n🔍 [{i}/5] Testing: {row['Card Name']}")
            print(f"    URL: {row['Card URL']}")
            
            # Scrape the card
            card_data = scraper.scrape_card_advanced(
                row['Issuer Name'], 
                row['Card Name'], 
                row['Card URL'],
                force_selenium=True  # Use Selenium for Bank of America
            )
            
            if card_data:
                results.append(card_data)
                
                # Display results immediately
                print(f"\n✅ Results for {card_data['card_name']}:")
                print(f"   Method: {card_data.get('scraping_method', 'unknown')}")
                print(f"   Annual Fee: {card_data.get('annual_fee', 'Not found')}")
                print(f"   Rewards: {len(card_data.get('rewards_rate', {}))} categories found")
                for category, rate in list(card_data.get('rewards_rate', {}).items())[:3]:
                    print(f"     • {rate} for {category[:50]}...")
                print(f"   Signup Bonus: {card_data.get('signup_bonus', 'Not found')}")
                print(f"   APR: {card_data.get('apr', 'Not found')}")
                print(f"   Benefits: {len(card_data.get('benefits', []))} found")
                for benefit in card_data.get('benefits', [])[:3]:
                    print(f"     • {benefit['type']}: {benefit['matched_text']}")
                
            else:
                print(f"❌ Failed to scrape {row['Card Name']}")
            
            # Be polite - wait between requests
            print("⏳ Waiting 3 seconds...")
            time.sleep(3)
    
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
    finally:
        scraper.cleanup()
    
    # Save results
    if results:
        filename = 'bofa_test_results.json'
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n💾 Saved {len(results)} Bank of America card results to {filename}")
        
        # Summary
        print(f"\n📊 BANK OF AMERICA TEST SUMMARY:")
        print(f"   Cards tested: {len(results)}")
        print(f"   Success rate: {len(results)}/{len(test_cards)} ({len(results)/len(test_cards)*100:.1f}%)")
        
        # Quick analysis
        fees_found = sum(1 for r in results if r.get('annual_fee'))
        rewards_found = sum(1 for r in results if r.get('rewards_rate'))
        bonuses_found = sum(1 for r in results if r.get('signup_bonus'))
        benefits_found = sum(len(r.get('benefits', [])) for r in results)
        apr_found = sum(1 for r in results if r.get('apr'))
        
        print(f"   Annual fees extracted: {fees_found}/{len(results)}")
        print(f"   Rewards rates extracted: {rewards_found}/{len(results)}")
        print(f"   Signup bonuses extracted: {bonuses_found}/{len(results)}")
        print(f"   APR rates extracted: {apr_found}/{len(results)}")
        print(f"   Total benefits extracted: {benefits_found}")
        
        if benefits_found > 0:
            avg_benefits = benefits_found / len(results)
            print(f"   Average benefits per card: {avg_benefits:.1f}")
    
    return results

def analyze_bofa_vs_others():
    """Compare Bank of America results with our previous results"""
    try:
        # Load Bank of America results
        with open('bofa_test_results.json', 'r') as f:
            bofa_results = json.load(f)
        
        # Load Citi results
        with open('citi_test_results.json', 'r') as f:
            citi_results = json.load(f)
        
        # Load Amex results
        with open('advanced_test_results.json', 'r') as f:
            amex_results = json.load(f)
        
        print(f"\n🔍 COMPARISON: Bank of America vs Citi vs American Express")
        print("=" * 70)
        
        print(f"📊 Data Extraction Comparison:")
        print(f"   Bank of America Cards Tested: {len(bofa_results)}")
        print(f"   Citi Cards Tested: {len(citi_results)}")
        print(f"   Amex Cards Tested: {len(amex_results)}")
        
        # Compare extraction success rates
        bofa_fees = sum(1 for r in bofa_results if r.get('annual_fee'))
        citi_fees = sum(1 for r in citi_results if r.get('annual_fee'))
        amex_fees = sum(1 for r in amex_results if r.get('annual_fee'))
        
        bofa_rewards = sum(1 for r in bofa_results if r.get('rewards_rate'))
        citi_rewards = sum(1 for r in citi_results if r.get('rewards_rate'))
        amex_rewards = sum(1 for r in amex_results if r.get('rewards_rate'))
        
        bofa_benefits = sum(len(r.get('benefits', [])) for r in bofa_results)
        citi_benefits = sum(len(r.get('benefits', [])) for r in citi_results)
        amex_benefits = sum(len(r.get('benefits', [])) for r in amex_results)
        
        bofa_apr = sum(1 for r in bofa_results if r.get('apr'))
        citi_apr = sum(1 for r in citi_results if r.get('apr'))
        amex_apr = sum(1 for r in amex_results if r.get('apr'))
        
        print(f"\n📈 Success Rates:")
        print(f"   Annual Fees:")
        print(f"     • Bank of America: {bofa_fees}/{len(bofa_results)} ({bofa_fees/len(bofa_results)*100:.1f}%)")
        print(f"     • Citi: {citi_fees}/{len(citi_results)} ({citi_fees/len(citi_results)*100:.1f}%)")
        print(f"     • Amex: {amex_fees}/{len(amex_results)} ({amex_fees/len(amex_results)*100:.1f}%)")
        
        print(f"   Rewards Rates:")
        print(f"     • Bank of America: {bofa_rewards}/{len(bofa_results)} ({bofa_rewards/len(bofa_results)*100:.1f}%)")
        print(f"     • Citi: {citi_rewards}/{len(citi_results)} ({citi_rewards/len(citi_results)*100:.1f}%)")
        print(f"     • Amex: {amex_rewards}/{len(amex_results)} ({amex_rewards/len(amex_results)*100:.1f}%)")
        
        print(f"   APR Rates:")
        print(f"     • Bank of America: {bofa_apr}/{len(bofa_results)} ({bofa_apr/len(bofa_results)*100:.1f}%)")
        print(f"     • Citi: {citi_apr}/{len(citi_results)} ({citi_apr/len(citi_results)*100:.1f}%)")
        print(f"     • Amex: {amex_apr}/{len(amex_results)} ({amex_apr/len(amex_results)*100:.1f}%)")
        
        print(f"   Benefits (Total):")
        print(f"     • Bank of America: {bofa_benefits} total")
        print(f"     • Citi: {citi_benefits} total")
        print(f"     • Amex: {amex_benefits} total")
        
        if len(bofa_results) > 0 and len(citi_results) > 0 and len(amex_results) > 0:
            bofa_avg_benefits = bofa_benefits / len(bofa_results)
            citi_avg_benefits = citi_benefits / len(citi_results)
            amex_avg_benefits = amex_benefits / len(amex_results)
            print(f"   Benefits (Average per Card):")
            print(f"     • Bank of America: {bofa_avg_benefits:.1f}")
            print(f"     • Citi: {citi_avg_benefits:.1f}")
            print(f"     • Amex: {amex_avg_benefits:.1f}")
        
        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT:")
        total_cards = len(bofa_results) + len(citi_results) + len(amex_results)
        total_benefits = bofa_benefits + citi_benefits + amex_benefits
        avg_benefits_overall = total_benefits / total_cards if total_cards > 0 else 0
        
        print(f"   Total cards tested across all banks: {total_cards}")
        print(f"   Total benefits extracted: {total_benefits}")
        print(f"   Overall average benefits per card: {avg_benefits_overall:.1f}")
        print(f"   Cross-bank compatibility: ✅ EXCELLENT")
        
    except FileNotFoundError as e:
        print(f"⚠️ Could not load results for comparison: {e}")

def show_detailed_bofa_results():
    """Show detailed results for each Bank of America card"""
    try:
        with open('bofa_test_results.json', 'r') as f:
            bofa_results = json.load(f)
        
        print(f"\n📋 DETAILED BANK OF AMERICA RESULTS:")
        print("=" * 70)
        
        for i, card in enumerate(bofa_results, 1):
            print(f"\n🏦 [{i}] {card['card_name']}")
            print(f"   Annual Fee: {card.get('annual_fee', 'Not found')}")
            print(f"   APR: {card.get('apr', 'Not found')}")
            print(f"   Signup Bonus: {card.get('signup_bonus', 'Not found')}")
            
            rewards = card.get('rewards_rate', {})
            if rewards:
                print(f"   Rewards ({len(rewards)} categories):")
                for category, rate in rewards.items():
                    print(f"     • {rate} for {category[:60]}...")
            else:
                print(f"   Rewards: None found")
            
            benefits = card.get('benefits', [])
            if benefits:
                print(f"   Benefits ({len(benefits)} found):")
                for benefit in benefits[:5]:  # Show first 5 benefits
                    print(f"     • {benefit['type']}: {benefit['matched_text'][:50]}...")
            else:
                print(f"   Benefits: None found")
            
            print(f"   Scraping Method: {card.get('scraping_method', 'unknown')}")
    
    except FileNotFoundError:
        print("⚠️ Bank of America results file not found. Run the test first.")

if __name__ == "__main__":
    print("🚀 Starting Bank of America Cards Scraping Test")
    print("🎯 Goal: Test our scraper on Bank of America (25 cards total)")
    print("📊 This will help us understand Bank of America's website structure")
    print()
    
    # Test Bank of America cards
    results = test_bank_of_america_cards()
    
    # Show detailed results
    if results:
        show_detailed_bofa_results()
        
        # Compare with other banks
        analyze_bofa_vs_others()
    
    print("\n✅ Bank of America testing complete!")
    print("💡 Next: Based on results, we can test Wells Fargo and U.S. Bank")
