#!/usr/bin/env python3
"""
AI-Powered Credit Card Data Cleaner
Uses intelligent algorithms to clean and fix scraped credit card data
"""

import json
import re
import pandas as pd
from typing import Dict, List, Optional, Tuple
from collections import Counter
import difflib

class AIDataCleaner:
    def __init__(self, raw_data_file: str):
        """Initialize the AI data cleaner"""
        self.raw_data_file = raw_data_file
        self.raw_data = self.load_raw_data()
        self.cleaned_data = []
        
        # Known card name patterns for validation
        self.known_card_patterns = [
            r'.*cash.*card',
            r'.*rewards.*card', 
            r'.*travel.*card',
            r'.*business.*card',
            r'.*platinum.*card',
            r'.*gold.*card',
            r'.*preferred.*card',
            r'.*sapphire.*',
            r'.*venture.*',
            r'.*freedom.*'
        ]
        
        # Common reward categories
        self.reward_categories = {
            'dining': ['dining', 'restaurant', 'food', 'cafe'],
            'travel': ['travel', 'airline', 'flight', 'hotel'],
            'gas': ['gas', 'fuel', 'gasoline', 'station'],
            'groceries': ['grocery', 'groceries', 'supermarket'],
            'entertainment': ['entertainment', 'streaming', 'movies'],
            'shopping': ['shopping', 'retail', 'purchases'],
            'business': ['business', 'office', 'supplies']
        }
        
        print(f"🤖 AI Data Cleaner initialized with {len(self.raw_data)} cards")
    
    def load_raw_data(self) -> List[Dict]:
        """Load the raw scraped data"""
        try:
            with open(self.raw_data_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return []
    
    def clean_card_name(self, card_name: str, url: str = "") -> str:
        """Use AI to clean and fix card names"""
        if not card_name or card_name.lower() in ['not found', 'none', '']:
            return "Unknown Card"
        
        # Remove common junk patterns
        cleaned = card_name
        
        # Remove URLs and domains
        cleaned = re.sub(r'https?://[^\s]+', '', cleaned)
        cleaned = re.sub(r'www\.[^\s]+', '', cleaned)
        cleaned = re.sub(r'\.com[^\s]*', '', cleaned)
        
        # Remove common navigation text
        junk_patterns = [
            r'credit cards?',
            r'apply now',
            r'learn more',
            r'compare',
            r'card details',
            r'important pricing',
            r'information',
            r'quick links?',
            r'balance transfer',
            r'intro apr'
        ]
        
        for pattern in junk_patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE)
        
        # Clean up whitespace and special characters
        cleaned = re.sub(r'[^\w\s®™]', ' ', cleaned)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        # If still looks like junk, try to extract from URL
        if len(cleaned) < 5 or not any(char.isalpha() for char in cleaned):
            url_card_name = self.extract_card_name_from_url(url)
            if url_card_name:
                cleaned = url_card_name
        
        # Capitalize properly
        cleaned = self.smart_capitalize(cleaned)
        
        return cleaned if cleaned else "Unknown Card"
    
    def extract_card_name_from_url(self, url: str) -> Optional[str]:
        """Extract card name from URL patterns"""
        if not url:
            return None
        
        # Common URL patterns
        patterns = [
            r'/([^/]+)-credit-card',
            r'/([^/]+)-card',
            r'cards/([^/]+)',
            r'/credit-cards/([^/]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                name = match.group(1).replace('-', ' ').replace('_', ' ')
                return self.smart_capitalize(name)
        
        return None
    
    def smart_capitalize(self, text: str) -> str:
        """Smart capitalization for card names"""
        if not text:
            return text
        
        # Special cases
        special_words = {
            'amex': 'American Express',
            'citi': 'Citi',
            'boa': 'Bank of America',
            'chase': 'Chase',
            'wells': 'Wells Fargo',
            'usbank': 'U.S. Bank',
            'sapphire': 'Sapphire',
            'venture': 'Venture',
            'freedom': 'Freedom',
            'platinum': 'Platinum',
            'gold': 'Gold',
            'preferred': 'Preferred',
            'rewards': 'Rewards',
            'cashback': 'Cashback',
            'business': 'Business'
        }
        
        words = text.lower().split()
        result = []
        
        for word in words:
            if word in special_words:
                result.append(special_words[word])
            elif len(word) > 2:
                result.append(word.capitalize())
            else:
                result.append(word.upper())
        
        return ' '.join(result)
    
    def clean_rewards_rate(self, rewards_rate: Dict) -> Dict:
        """Clean and fix rewards rate data"""
        if not rewards_rate:
            return {}
        
        cleaned_rewards = {}
        
        for category, rate in rewards_rate.items():
            # Clean the category name
            clean_category = self.clean_reward_category(category)
            
            # Clean the rate value
            clean_rate = self.clean_reward_rate(rate)
            
            # Only keep if both are valid
            if clean_category and clean_rate:
                cleaned_rewards[clean_category] = clean_rate
        
        return cleaned_rewards
    
    def clean_reward_category(self, category: str) -> Optional[str]:
        """Clean reward category text"""
        if not category:
            return None
        
        # Remove junk text and numbers
        cleaned = re.sub(r'\d+[kK]?\s*(bonus|miles|points)', '', category)
        cleaned = re.sub(r'after\s+.*', '', cleaned)
        cleaned = re.sub(r'qualifying.*', '', cleaned)
        cleaned = re.sub(r'eligible\s+', '', cleaned)
        cleaned = re.sub(r'purchases?\s*', '', cleaned)
        
        # Extract meaningful category
        for standard_category, keywords in self.reward_categories.items():
            for keyword in keywords:
                if keyword in cleaned.lower():
                    return standard_category
        
        # Try to extract specific categories
        if 'restaurant' in cleaned.lower() or 'dining' in cleaned.lower():
            return 'dining'
        elif 'travel' in cleaned.lower() or 'airline' in cleaned.lower():
            return 'travel'
        elif 'gas' in cleaned.lower() or 'fuel' in cleaned.lower():
            return 'gas'
        elif 'grocery' in cleaned.lower():
            return 'groceries'
        elif 'business' in cleaned.lower():
            return 'business'
        
        # If still too long or contains junk, skip
        if len(cleaned) > 50 or any(junk in cleaned.lower() for junk in ['bonus', 'after', 'qualifying']):
            return None
        
        return cleaned.strip() if len(cleaned.strip()) > 3 else None
    
    def clean_reward_rate(self, rate: str) -> Optional[str]:
        """Clean reward rate values"""
        if not rate:
            return None
        
        # Extract numeric rate
        rate_patterns = [
            r'(\d+(?:\.\d+)?)x',
            r'(\d+(?:\.\d+)?)%',
            r'(\d+(?:\.\d+)?)\s*points',
            r'(\d+(?:\.\d+)?)\s*miles'
        ]
        
        for pattern in rate_patterns:
            match = re.search(pattern, rate.lower())
            if match:
                value = float(match.group(1))
                if 0.5 <= value <= 10:  # Reasonable range
                    if 'x' in rate.lower() or 'points' in rate.lower() or 'miles' in rate.lower():
                        return f"{value}x"
                    elif '%' in rate.lower():
                        return f"{value}%"
                    else:
                        return f"{value}x"
        
        return None
    
    def clean_annual_fee(self, fee: str) -> int:
        """Clean annual fee data"""
        if not fee:
            return 0
        
        # Remove common text
        fee_text = str(fee).lower().replace('$', '').replace(',', '')
        
        # Extract number
        numbers = re.findall(r'\d+', fee_text)
        
        if not numbers:
            return 0
        
        # Take the first reasonable number
        for num in numbers:
            value = int(num)
            if 0 <= value <= 2000:  # Reasonable fee range
                return value
        
        return 0
    
    def clean_benefits(self, benefits: List[Dict]) -> List[Dict]:
        """Clean benefits data"""
        if not benefits:
            return []
        
        cleaned_benefits = []
        seen_benefits = set()
        
        for benefit in benefits:
            if not isinstance(benefit, dict):
                continue
            
            benefit_type = benefit.get('type', '')
            matched_text = benefit.get('matched_text', '')
            
            # Skip if too long (likely scraped junk)
            if len(matched_text) > 200:
                continue
            
            # Skip duplicates
            benefit_key = f"{benefit_type}:{matched_text}"
            if benefit_key in seen_benefits:
                continue
            
            # Clean the matched text
            clean_text = self.clean_benefit_text(matched_text)
            
            if clean_text and len(clean_text) > 5:
                cleaned_benefits.append({
                    'type': benefit_type,
                    'matched_text': clean_text,
                    'description': benefit.get('description', '')[:200]  # Truncate description
                })
                seen_benefits.add(benefit_key)
        
        return cleaned_benefits
    
    def clean_benefit_text(self, text: str) -> str:
        """Clean individual benefit text"""
        if not text:
            return ""
        
        # Remove common junk
        cleaned = text
        cleaned = re.sub(r'apply now.*', '', cleaned, flags=re.IGNORECASE)
        cleaned = re.sub(r'learn more.*', '', cleaned, flags=re.IGNORECASE)
        cleaned = re.sub(r'card details.*', '', cleaned, flags=re.IGNORECASE)
        cleaned = re.sub(r'important pricing.*', '', cleaned, flags=re.IGNORECASE)
        
        # Clean up
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        return cleaned
    
    def validate_card_data(self, card: Dict) -> bool:
        """Validate if card data looks reasonable"""
        # Must have a reasonable card name
        card_name = card.get('card_name', '')
        if not card_name or len(card_name) < 5:
            return False
        
        # Must have an issuer
        issuer = card.get('issuer', '')
        if not issuer:
            return False
        
        # Annual fee should be reasonable
        annual_fee = card.get('annual_fee_cleaned', 0)
        if annual_fee < 0 or annual_fee > 2000:
            return False
        
        return True
    
    def clean_all_data(self) -> List[Dict]:
        """Clean all credit card data using AI"""
        print("🤖 Starting AI-powered data cleaning...")
        
        cleaned_count = 0
        skipped_count = 0
        
        for i, card in enumerate(self.raw_data):
            try:
                # Clean each field
                cleaned_card = {
                    'issuer': card.get('issuer', ''),
                    'card_name': self.clean_card_name(card.get('card_name', ''), card.get('url', '')),
                    'url': card.get('url', ''),
                    'scraped_at': card.get('scraped_at', ''),
                    'annual_fee': card.get('annual_fee', ''),
                    'annual_fee_cleaned': self.clean_annual_fee(card.get('annual_fee', '')),
                    'rewards_rate': self.clean_rewards_rate(card.get('rewards_rate', {})),
                    'signup_bonus': card.get('signup_bonus', ''),
                    'apr': card.get('apr', ''),
                    'benefits': self.clean_benefits(card.get('benefits', [])),
                    'categories': card.get('categories', []),
                    'scraping_method': card.get('scraping_method', ''),
                    'meta_description': card.get('meta_description', ''),
                    'page_title': card.get('page_title', '')
                }
                
                # Validate the cleaned card
                if self.validate_card_data(cleaned_card):
                    self.cleaned_data.append(cleaned_card)
                    cleaned_count += 1
                else:
                    skipped_count += 1
                    print(f"⚠️ Skipped invalid card: {cleaned_card['card_name']}")
                
                # Progress update
                if (i + 1) % 10 == 0:
                    print(f"🔄 Processed {i + 1}/{len(self.raw_data)} cards...")
            
            except Exception as e:
                print(f"❌ Error cleaning card {i}: {e}")
                skipped_count += 1
        
        print(f"✅ AI cleaning complete!")
        print(f"📊 Results: {cleaned_count} cleaned, {skipped_count} skipped")
        
        return self.cleaned_data
    
    def save_cleaned_data(self, output_file: str = 'cleaned_credit_cards.json'):
        """Save the cleaned data"""
        with open(output_file, 'w') as f:
            json.dump(self.cleaned_data, f, indent=2)
        
        print(f"💾 Cleaned data saved to: {output_file}")
        return output_file
    
    def generate_cleaning_report(self) -> Dict:
        """Generate a report on the cleaning process"""
        if not self.cleaned_data:
            return {}
        
        # Analyze cleaned data
        total_cards = len(self.cleaned_data)
        cards_with_rewards = sum(1 for card in self.cleaned_data if card['rewards_rate'])
        cards_with_benefits = sum(1 for card in self.cleaned_data if card['benefits'])
        total_benefits = sum(len(card['benefits']) for card in self.cleaned_data)
        
        # Fee distribution
        fees = [card['annual_fee_cleaned'] for card in self.cleaned_data]
        free_cards = sum(1 for fee in fees if fee == 0)
        
        # Issuer distribution
        issuers = [card['issuer'] for card in self.cleaned_data]
        issuer_counts = Counter(issuers)
        
        report = {
            'cleaning_summary': {
                'total_cards_cleaned': total_cards,
                'cards_with_rewards': cards_with_rewards,
                'cards_with_benefits': cards_with_benefits,
                'total_benefits_extracted': total_benefits,
                'average_benefits_per_card': total_benefits / total_cards if total_cards > 0 else 0
            },
            'fee_analysis': {
                'free_cards': free_cards,
                'paid_cards': total_cards - free_cards,
                'average_fee': sum(fees) / len(fees) if fees else 0
            },
            'issuer_distribution': dict(issuer_counts.most_common()),
            'data_quality_improvements': {
                'malformed_card_names_fixed': 'Yes',
                'reward_rates_standardized': 'Yes', 
                'benefits_deduplicated': 'Yes',
                'annual_fees_normalized': 'Yes'
            }
        }
        
        return report

def main():
    """Main cleaning function"""
    print("🤖 AI-POWERED CREDIT CARD DATA CLEANER")
    print("🎯 Fixing scraped data using intelligent algorithms")
    print("=" * 60)
    
    # Find the latest raw data file
    import glob
    result_files = glob.glob('scraping_results_*/all_cards_results_*.json')
    
    if not result_files:
        print("❌ No raw data files found")
        return
    
    latest_file = max(result_files)
    print(f"📁 Using raw data: {latest_file}")
    
    # Initialize cleaner
    cleaner = AIDataCleaner(latest_file)
    
    # Clean the data
    cleaned_data = cleaner.clean_all_data()
    
    # Save cleaned data
    output_file = cleaner.save_cleaned_data()
    
    # Generate report
    report = cleaner.generate_cleaning_report()
    
    print(f"\n📊 CLEANING REPORT:")
    print(f"✅ Total cards cleaned: {report['cleaning_summary']['total_cards_cleaned']}")
    print(f"🎁 Cards with rewards: {report['cleaning_summary']['cards_with_rewards']}")
    print(f"✨ Cards with benefits: {report['cleaning_summary']['cards_with_benefits']}")
    print(f"🏆 Total benefits: {report['cleaning_summary']['total_benefits_extracted']}")
    print(f"💰 Free cards: {report['fee_analysis']['free_cards']}")
    
    print(f"\n🏦 TOP ISSUERS:")
    for issuer, count in list(report['issuer_distribution'].items())[:5]:
        print(f"   {issuer}: {count} cards")
    
    # Save report
    with open('cleaning_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n🎉 AI CLEANING COMPLETE!")
    print(f"📁 Cleaned data: {output_file}")
    print(f"📊 Report: cleaning_report.json")
    print(f"💡 Ready to update AI recommendations with clean data!")

if __name__ == "__main__":
    main()
