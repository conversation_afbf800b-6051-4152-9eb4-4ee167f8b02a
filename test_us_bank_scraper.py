#!/usr/bin/env python3
"""
Test U.S. Bank Cards Scraping - Final Bank Testing
Tests our advanced scraper on U.S. Bank credit cards to complete our cross-bank testing
"""

import pandas as pd
from advanced_credit_card_scraper import AdvancedCreditCardScraper
import time
import json

def test_us_bank_cards():
    """Test scraper specifically on U.S. Bank cards"""
    
    # Load the CSV and filter for U.S. Bank cards
    df = pd.read_csv('credit_cards_validated.csv')
    us_bank_cards = df[df['Issuer Name'] == 'U.S. Bank'].copy()
    
    print(f"🏦 Found {len(us_bank_cards)} U.S. Bank cards to test")
    print("\n📋 U.S. Bank Cards List:")
    for i, (_, row) in enumerate(us_bank_cards.iterrows(), 1):
        print(f"   {i}. {row['Card Name']}")
    
    # Initialize scraper
    scraper = AdvancedCreditCardScraper()
    
    # Test with first 5 U.S. Bank cards to get a good sample
    test_cards = us_bank_cards.head(5)
    
    print(f"\n🧪 Testing with first 5 U.S. Bank cards...")
    print("=" * 70)
    
    results = []
    
    try:
        for i, (_, row) in enumerate(test_cards.iterrows(), 1):
            print(f"\n🔍 [{i}/5] Testing: {row['Card Name']}")
            print(f"    URL: {row['Card URL']}")
            
            # Scrape the card
            card_data = scraper.scrape_card_advanced(
                row['Issuer Name'], 
                row['Card Name'], 
                row['Card URL'],
                force_selenium=True  # Use Selenium for U.S. Bank
            )
            
            if card_data:
                results.append(card_data)
                
                # Display results immediately
                print(f"\n✅ Results for {card_data['card_name']}:")
                print(f"   Method: {card_data.get('scraping_method', 'unknown')}")
                print(f"   Annual Fee: {card_data.get('annual_fee', 'Not found')}")
                print(f"   Rewards: {len(card_data.get('rewards_rate', {}))} categories found")
                for category, rate in list(card_data.get('rewards_rate', {}).items())[:3]:
                    print(f"     • {rate} for {category[:50]}...")
                print(f"   Signup Bonus: {card_data.get('signup_bonus', 'Not found')}")
                print(f"   APR: {card_data.get('apr', 'Not found')}")
                print(f"   Benefits: {len(card_data.get('benefits', []))} found")
                for benefit in card_data.get('benefits', [])[:3]:
                    print(f"     • {benefit['type']}: {benefit['matched_text']}")
                
            else:
                print(f"❌ Failed to scrape {row['Card Name']}")
            
            # Be polite - wait between requests
            print("⏳ Waiting 3 seconds...")
            time.sleep(3)
    
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
    finally:
        scraper.cleanup()
    
    # Save results
    if results:
        filename = 'us_bank_test_results.json'
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n💾 Saved {len(results)} U.S. Bank card results to {filename}")
        
        # Summary
        print(f"\n📊 U.S. BANK TEST SUMMARY:")
        print(f"   Cards tested: {len(results)}")
        print(f"   Success rate: {len(results)}/{len(test_cards)} ({len(results)/len(test_cards)*100:.1f}%)")
        
        # Quick analysis
        fees_found = sum(1 for r in results if r.get('annual_fee'))
        rewards_found = sum(1 for r in results if r.get('rewards_rate'))
        bonuses_found = sum(1 for r in results if r.get('signup_bonus'))
        benefits_found = sum(len(r.get('benefits', [])) for r in results)
        apr_found = sum(1 for r in results if r.get('apr'))
        
        print(f"   Annual fees extracted: {fees_found}/{len(results)}")
        print(f"   Rewards rates extracted: {rewards_found}/{len(results)}")
        print(f"   Signup bonuses extracted: {bonuses_found}/{len(results)}")
        print(f"   APR rates extracted: {apr_found}/{len(results)}")
        print(f"   Total benefits extracted: {benefits_found}")
        
        if benefits_found > 0:
            avg_benefits = benefits_found / len(results)
            print(f"   Average benefits per card: {avg_benefits:.1f}")
    
    return results

def create_final_comprehensive_analysis():
    """Create the ultimate comprehensive analysis of ALL bank testing"""
    try:
        # Load all bank results
        banks_files = {
            'U.S. Bank': 'us_bank_test_results.json',
            'Wells Fargo': 'wells_fargo_test_results.json',
            'Bank of America': 'bofa_test_results.json', 
            'Citi': 'citi_test_results.json',
            'American Express': 'advanced_test_results.json'
        }
        
        all_results = {}
        for bank_name, filename in banks_files.items():
            try:
                with open(filename, 'r') as f:
                    all_results[bank_name] = json.load(f)
                print(f"✅ Loaded {bank_name}: {len(all_results[bank_name])} cards")
            except FileNotFoundError:
                print(f"⚠️ {filename} not found, skipping {bank_name}")
        
        if not all_results:
            print("⚠️ No bank results found")
            return
        
        print(f"\n🏆 FINAL COMPREHENSIVE BANK TESTING ANALYSIS")
        print("=" * 80)
        
        # Create detailed summary table
        print(f"{'Bank':<20} {'Cards':<8} {'Success':<10} {'Fees':<8} {'Rewards':<10} {'APR':<8} {'Bonuses':<10} {'Benefits':<12} {'Avg/Card':<10}")
        print("-" * 100)
        
        total_cards = 0
        total_benefits = 0
        total_successful_banks = 0
        
        bank_stats = {}
        
        for bank_name, results in all_results.items():
            if not results:
                continue
                
            cards = len(results)
            fees = sum(1 for r in results if r.get('annual_fee'))
            rewards = sum(1 for r in results if r.get('rewards_rate'))
            apr = sum(1 for r in results if r.get('apr'))
            bonuses = sum(1 for r in results if r.get('signup_bonus'))
            benefits = sum(len(r.get('benefits', [])) for r in results)
            avg_benefits = benefits / cards if cards > 0 else 0
            
            # Calculate success rate (assuming we tested 5 cards per bank, except for some exceptions)
            target_cards = 5 if bank_name != 'Bank of America' else 5  # BoA had timeouts
            success_rate = f"{cards}/{target_cards}"
            
            total_cards += cards
            total_benefits += benefits
            total_successful_banks += 1
            
            bank_stats[bank_name] = {
                'cards': cards,
                'fees': fees,
                'rewards': rewards,
                'apr': apr,
                'bonuses': bonuses,
                'benefits': benefits,
                'avg_benefits': avg_benefits
            }
            
            print(f"{bank_name:<20} {cards:<8} {success_rate:<10} {fees:<8} {rewards:<10} {apr:<8} {bonuses:<10} {benefits:<12} {avg_benefits:<10.1f}")
        
        print("-" * 100)
        print(f"{'TOTAL':<20} {total_cards:<8} {'':<10} {'':<8} {'':<10} {'':<8} {'':<10} {total_benefits:<12} {total_benefits/total_cards if total_cards > 0 else 0:<10.1f}")
        
        # Overall assessment
        print(f"\n🎯 FINAL ASSESSMENT:")
        print(f"   🏦 Banks successfully tested: {total_successful_banks}/5")
        print(f"   📊 Total cards scraped: {total_cards}")
        print(f"   🎁 Total benefits extracted: {total_benefits}")
        print(f"   📈 Overall average benefits per card: {total_benefits/total_cards if total_cards > 0 else 0:.1f}")
        
        # Bank rankings
        print(f"\n🏆 BANK PERFORMANCE RANKINGS:")
        sorted_banks = sorted(bank_stats.items(), key=lambda x: x[1]['avg_benefits'], reverse=True)
        
        for i, (bank_name, stats) in enumerate(sorted_banks, 1):
            print(f"   {i}. {bank_name}: {stats['avg_benefits']:.1f} benefits/card ({stats['cards']} cards)")
        
        # Cross-bank compatibility assessment
        successful_banks = len([b for b in all_results.values() if b])
        compatibility_score = successful_banks / 5 * 100
        
        print(f"\n✅ CROSS-BANK COMPATIBILITY:")
        print(f"   Success Rate: {compatibility_score:.0f}% ({successful_banks}/5 banks)")
        print(f"   Status: {'🎉 EXCELLENT' if compatibility_score >= 80 else '⚠️ NEEDS WORK'}")
        
        # Readiness assessment
        print(f"\n🚀 FULL-SCALE SCRAPING READINESS:")
        if compatibility_score >= 80 and total_cards >= 15:
            print(f"   Status: ✅ READY FOR FULL SCALE!")
            print(f"   Confidence: HIGH - Proven across {successful_banks} banks")
            print(f"   Recommendation: Proceed with all 97 cards")
        else:
            print(f"   Status: ⚠️ NEEDS MORE TESTING")
            print(f"   Recommendation: Fix issues before full scale")
        
        # Save comprehensive analysis
        analysis_data = {
            'banks_tested': successful_banks,
            'total_cards': total_cards,
            'total_benefits': total_benefits,
            'avg_benefits_per_card': total_benefits/total_cards if total_cards > 0 else 0,
            'compatibility_score': compatibility_score,
            'bank_stats': bank_stats,
            'ready_for_full_scale': compatibility_score >= 80 and total_cards >= 15
        }
        
        with open('final_bank_analysis.json', 'w') as f:
            json.dump(analysis_data, f, indent=2)
        
        print(f"\n💾 Comprehensive analysis saved to: final_bank_analysis.json")
        
    except Exception as e:
        print(f"❌ Error creating final analysis: {e}")

def show_detailed_us_bank_results():
    """Show detailed results for each U.S. Bank card"""
    try:
        with open('us_bank_test_results.json', 'r') as f:
            us_bank_results = json.load(f)
        
        print(f"\n📋 DETAILED U.S. BANK RESULTS:")
        print("=" * 70)
        
        for i, card in enumerate(us_bank_results, 1):
            print(f"\n🏦 [{i}] {card['card_name']}")
            print(f"   Annual Fee: {card.get('annual_fee', 'Not found')}")
            print(f"   APR: {card.get('apr', 'Not found')}")
            print(f"   Signup Bonus: {card.get('signup_bonus', 'Not found')}")
            
            rewards = card.get('rewards_rate', {})
            if rewards:
                print(f"   Rewards ({len(rewards)} categories):")
                for category, rate in rewards.items():
                    print(f"     • {rate} for {category[:60]}...")
            else:
                print(f"   Rewards: None found")
            
            benefits = card.get('benefits', [])
            if benefits:
                print(f"   Benefits ({len(benefits)} found):")
                for benefit in benefits[:5]:  # Show first 5 benefits
                    print(f"     • {benefit['type']}: {benefit['matched_text'][:50]}...")
            else:
                print(f"   Benefits: None found")
            
            print(f"   Scraping Method: {card.get('scraping_method', 'unknown')}")
            
            # Show meta description for context
            meta_desc = card.get('meta_description', '')
            if meta_desc:
                print(f"   Meta Description: {meta_desc[:100]}...")
    
    except FileNotFoundError:
        print("⚠️ U.S. Bank results file not found. Run the test first.")

if __name__ == "__main__":
    print("🚀 Starting U.S. Bank Cards Scraping Test - FINAL BANK!")
    print("🎯 Goal: Complete our cross-bank testing with U.S. Bank (15 cards total)")
    print("📊 This will give us 100% bank coverage for our scraper")
    print("🏆 After this, we'll be ready for full-scale 97-card scraping!")
    print()
    
    # Test U.S. Bank cards
    results = test_us_bank_cards()
    
    # Show detailed results
    if results:
        show_detailed_us_bank_results()
        
        # Create the ultimate comprehensive analysis
        create_final_comprehensive_analysis()
    
    print("\n✅ U.S. Bank testing complete!")
    print("🎉 ALL BANKS TESTED - Ready for full-scale scraping!")
    print("💡 Next: Run the production scraper on all 97 cards")
