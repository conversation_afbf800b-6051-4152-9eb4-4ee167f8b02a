#!/usr/bin/env python3
"""
Test Citi Cards Scraping - Scale Up Phase
Tests our advanced scraper on Citi bank credit cards
"""

import pandas as pd
from advanced_credit_card_scraper import AdvancedCreditCardScraper
import time
import json

def test_citi_cards():
    """Test scraper specifically on Citi cards"""
    
    # Load the CSV and filter for Citi cards
    df = pd.read_csv('credit_cards_validated.csv')
    citi_cards = df[df['Issuer Name'] == 'Citi'].copy()
    
    print(f"🏦 Found {len(citi_cards)} Citi cards to test")
    print("\n📋 Citi Cards List:")
    for i, (_, row) in enumerate(citi_cards.iterrows(), 1):
        print(f"   {i}. {row['Card Name']}")
    
    # Initialize scraper
    scraper = AdvancedCreditCardScraper()
    
    # Test with first 3 Citi cards to start
    test_cards = citi_cards.head(3)
    
    print(f"\n🧪 Testing with first 3 Citi cards...")
    print("=" * 60)
    
    results = []
    
    try:
        for i, (_, row) in enumerate(test_cards.iterrows(), 1):
            print(f"\n🔍 [{i}/3] Testing: {row['Card Name']}")
            print(f"    URL: {row['Card URL']}")
            
            # Scrape the card
            card_data = scraper.scrape_card_advanced(
                row['Issuer Name'], 
                row['Card Name'], 
                row['Card URL'],
                force_selenium=True  # Use Selenium for Citi
            )
            
            if card_data:
                results.append(card_data)
                
                # Display results immediately
                print(f"\n✅ Results for {card_data['card_name']}:")
                print(f"   Method: {card_data.get('scraping_method', 'unknown')}")
                print(f"   Annual Fee: {card_data.get('annual_fee', 'Not found')}")
                print(f"   Rewards: {len(card_data.get('rewards_rate', {}))} categories found")
                for category, rate in list(card_data.get('rewards_rate', {}).items())[:3]:
                    print(f"     • {rate} for {category[:50]}...")
                print(f"   Signup Bonus: {card_data.get('signup_bonus', 'Not found')}")
                print(f"   APR: {card_data.get('apr', 'Not found')}")
                print(f"   Benefits: {len(card_data.get('benefits', []))} found")
                for benefit in card_data.get('benefits', [])[:3]:
                    print(f"     • {benefit['type']}: {benefit['matched_text']}")
                
            else:
                print(f"❌ Failed to scrape {row['Card Name']}")
            
            # Be polite - wait between requests
            print("⏳ Waiting 3 seconds...")
            time.sleep(3)
    
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
    finally:
        scraper.cleanup()
    
    # Save results
    if results:
        filename = 'citi_test_results.json'
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n💾 Saved {len(results)} Citi card results to {filename}")
        
        # Summary
        print(f"\n📊 CITI CARDS TEST SUMMARY:")
        print(f"   Cards tested: {len(results)}")
        print(f"   Success rate: {len(results)}/{len(test_cards)} ({len(results)/len(test_cards)*100:.1f}%)")
        
        # Quick analysis
        fees_found = sum(1 for r in results if r.get('annual_fee'))
        rewards_found = sum(1 for r in results if r.get('rewards_rate'))
        bonuses_found = sum(1 for r in results if r.get('signup_bonus'))
        benefits_found = sum(len(r.get('benefits', [])) for r in results)
        
        print(f"   Annual fees extracted: {fees_found}/{len(results)}")
        print(f"   Rewards rates extracted: {rewards_found}/{len(results)}")
        print(f"   Signup bonuses extracted: {bonuses_found}/{len(results)}")
        print(f"   Total benefits extracted: {benefits_found}")
        
        if benefits_found > 0:
            avg_benefits = benefits_found / len(results)
            print(f"   Average benefits per card: {avg_benefits:.1f}")
    
    return results

def analyze_citi_vs_amex():
    """Compare Citi results with our previous Amex results"""
    try:
        # Load Citi results
        with open('citi_test_results.json', 'r') as f:
            citi_results = json.load(f)
        
        # Load Amex results
        with open('advanced_test_results.json', 'r') as f:
            amex_results = json.load(f)
        
        print(f"\n🔍 COMPARISON: Citi vs American Express")
        print("=" * 50)
        
        print(f"📊 Data Extraction Comparison:")
        print(f"   Citi Cards Tested: {len(citi_results)}")
        print(f"   Amex Cards Tested: {len(amex_results)}")
        
        # Compare extraction success rates
        citi_fees = sum(1 for r in citi_results if r.get('annual_fee'))
        amex_fees = sum(1 for r in amex_results if r.get('annual_fee'))
        
        citi_rewards = sum(1 for r in citi_results if r.get('rewards_rate'))
        amex_rewards = sum(1 for r in amex_results if r.get('rewards_rate'))
        
        citi_benefits = sum(len(r.get('benefits', [])) for r in citi_results)
        amex_benefits = sum(len(r.get('benefits', [])) for r in amex_results)
        
        print(f"\n📈 Success Rates:")
        print(f"   Annual Fees - Citi: {citi_fees}/{len(citi_results)} | Amex: {amex_fees}/{len(amex_results)}")
        print(f"   Rewards - Citi: {citi_rewards}/{len(citi_results)} | Amex: {amex_rewards}/{len(amex_results)}")
        print(f"   Benefits - Citi: {citi_benefits} total | Amex: {amex_benefits} total")
        
        if len(citi_results) > 0 and len(amex_results) > 0:
            citi_avg_benefits = citi_benefits / len(citi_results)
            amex_avg_benefits = amex_benefits / len(amex_results)
            print(f"   Avg Benefits per Card - Citi: {citi_avg_benefits:.1f} | Amex: {amex_avg_benefits:.1f}")
        
    except FileNotFoundError as e:
        print(f"⚠️ Could not load results for comparison: {e}")

if __name__ == "__main__":
    print("🚀 Starting Citi Cards Scraping Test")
    print("🎯 Goal: Test our scraper on a different bank (Citi)")
    print("📊 This will help us understand if we need bank-specific adjustments")
    print()
    
    # Test Citi cards
    results = test_citi_cards()
    
    # Compare with Amex if we have results
    if results:
        analyze_citi_vs_amex()
    
    print("\n✅ Citi testing complete!")
    print("💡 Next: Based on results, we can scale to all banks or make adjustments")
