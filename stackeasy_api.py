#!/usr/bin/env python3
"""
StackEasy API - REST API for Credit Card Recommendations
Exposes our AI recommendation engine to the frontend
"""

from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, List, Optional
import json
import pandas as pd
from datetime import datetime
import uvicorn

# Import our AI recommendation engine
from ai_recommendations import CreditCardRecommendationEngine

# Initialize FastAPI app
app = FastAPI(
    title="StackEasy Credit Card API",
    description="AI-powered credit card recommendations and insights",
    version="1.0.0"
)

# Add CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize AI engine globally
try:
    ai_engine = CreditCardRecommendationEngine()
    print("✅ AI Recommendation Engine loaded successfully")
except Exception as e:
    print(f"❌ Failed to load AI engine: {e}")
    ai_engine = None

# Pydantic models for request/response
class UserSpendingPattern(BaseModel):
    dining: float = 0
    travel: float = 0
    groceries: float = 0
    gas: float = 0
    entertainment: float = 0
    shopping: float = 0
    business: float = 0

class UserProfile(BaseModel):
    spending_patterns: UserSpendingPattern
    credit_score_range: str = "good"  # fair, good, excellent
    preferences: List[str] = []
    current_cards: List[str] = []
    monthly_income: Optional[float] = None

class CardRecommendationResponse(BaseModel):
    card_name: str
    issuer: str
    score: float
    annual_fee: int
    card_type: str
    total_benefits: int
    best_for: List[str]
    reason: Optional[str] = None

class SpendingInsightResponse(BaseModel):
    category: str
    amount: float
    percentage: float
    recommended_card: Optional[str] = None
    potential_additional_rewards: Optional[float] = None

class OptimizationTip(BaseModel):
    tip: str
    category: str
    potential_savings: float
    recommended_action: str

# API Endpoints

@app.get("/")
async def root():
    """API health check"""
    return {
        "message": "StackEasy Credit Card API",
        "status": "active",
        "ai_engine_status": "loaded" if ai_engine else "failed",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/cards/best-by-category/{category}")
async def get_best_cards_by_category(
    category: str,
    limit: int = Query(3, ge=1, le=10, description="Number of cards to return")
) -> List[CardRecommendationResponse]:
    """Get best credit cards for a specific spending category"""

    if not ai_engine:
        raise HTTPException(status_code=503, detail="AI engine not available")

    try:
        best_cards = ai_engine.get_best_cards_by_category(category, top_n=limit)

        return [
            CardRecommendationResponse(
                card_name=card['card_name'],
                issuer=card['issuer'],
                score=card['score'],
                annual_fee=card['annual_fee'],
                card_type=card['card_type'],
                total_benefits=card['total_benefits'],
                best_for=card['best_for']
            )
            for card in best_cards
        ]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting cards: {str(e)}")

@app.post("/api/analysis/spending-insights")
async def analyze_spending_patterns(user_profile: UserProfile) -> Dict:
    """Analyze user spending patterns and provide insights"""

    if not ai_engine:
        raise HTTPException(status_code=503, detail="AI engine not available")

    try:
        # Convert spending patterns to dict
        spending_dict = user_profile.spending_patterns.dict()

        # Get insights from AI engine
        insights = ai_engine.analyze_spending_patterns(spending_dict)

        # Format response
        response = {
            "total_monthly_spending": insights['total_monthly_spending'],
            "category_breakdown": [
                SpendingInsightResponse(
                    category=category,
                    amount=data['amount'],
                    percentage=data['percentage'],
                    recommended_card=insights['recommended_cards'].get(category, {}).get('card_name'),
                    potential_additional_rewards=next(
                        (opp['additional_monthly_rewards'] for opp in insights['optimization_opportunities']
                         if opp['category'] == category), None
                    )
                )
                for category, data in insights['category_breakdown'].items()
            ],
            "optimization_opportunities": insights['optimization_opportunities']
        }

        return response

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error analyzing spending: {str(e)}")

@app.post("/api/recommendations/personalized")
async def get_personalized_recommendations(user_profile: UserProfile) -> Dict:
    """Get personalized card recommendations based on user profile"""

    if not ai_engine:
        raise HTTPException(status_code=503, detail="AI engine not available")

    try:
        # Convert to format expected by AI engine
        profile_dict = {
            'spending_patterns': user_profile.spending_patterns.dict(),
            'credit_score_range': user_profile.credit_score_range,
            'preferences': user_profile.preferences,
            'current_cards': user_profile.current_cards
        }

        # Generate personalized dashboard
        dashboard = ai_engine.create_personalized_dashboard(profile_dict)

        return {
            "spending_insights": dashboard['spending_insights'],
            "best_cards_by_category": dashboard['best_cards_by_category'],
            "new_card_recommendations": dashboard['new_card_recommendations'][:5],
            "optimization_tips": [
                OptimizationTip(
                    tip=tip,
                    category="general",
                    potential_savings=0.0,  # Could be calculated from tip text
                    recommended_action="Consider switching cards"
                )
                for tip in dashboard['optimization_tips']
            ]
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating recommendations: {str(e)}")

@app.get("/api/cards/search")
async def search_cards(
    query: str = Query(..., description="Search term for card name or issuer"),
    card_type: Optional[str] = Query(None, description="Filter by card type"),
    max_annual_fee: Optional[int] = Query(None, description="Maximum annual fee"),
    limit: int = Query(10, ge=1, le=50, description="Number of results to return")
) -> List[Dict]:
    """Search for credit cards based on criteria"""

    if not ai_engine:
        raise HTTPException(status_code=503, detail="AI engine not available")

    try:
        # Load card profiles
        df = ai_engine.profiles_df

        # Apply filters
        filtered_df = df.copy()

        # Text search
        if query:
            mask = (
                filtered_df['card_name'].str.contains(query, case=False, na=False) |
                filtered_df['issuer'].str.contains(query, case=False, na=False)
            )
            filtered_df = filtered_df[mask]

        # Card type filter
        if card_type:
            filtered_df = filtered_df[filtered_df['card_type'].str.contains(card_type, case=False, na=False)]

        # Annual fee filter
        if max_annual_fee is not None:
            filtered_df = filtered_df[filtered_df['annual_fee'] <= max_annual_fee]

        # Limit results
        filtered_df = filtered_df.head(limit)

        # Convert to response format
        results = []
        for _, card in filtered_df.iterrows():
            results.append({
                "card_name": card['card_name'],
                "issuer": card['issuer'],
                "annual_fee": card['annual_fee'],
                "card_type": card['card_type'],
                "has_rewards": card['has_rewards'],
                "total_benefits": card['total_benefits'],
                "has_signup_bonus": card['has_signup_bonus']
            })

        return results

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error searching cards: {str(e)}")

@app.get("/api/stats/overview")
async def get_system_overview() -> Dict:
    """Get overview statistics of the credit card database"""

    if not ai_engine:
        raise HTTPException(status_code=503, detail="AI engine not available")

    try:
        df = ai_engine.profiles_df

        # Calculate statistics
        stats = {
            "total_cards": len(df),
            "cards_by_issuer": df['issuer'].value_counts().to_dict(),
            "cards_by_type": df['card_type'].value_counts().to_dict(),
            "fee_distribution": {
                "free_cards": len(df[df['annual_fee'] == 0]),
                "low_fee_cards": len(df[(df['annual_fee'] > 0) & (df['annual_fee'] <= 100)]),
                "mid_fee_cards": len(df[(df['annual_fee'] > 100) & (df['annual_fee'] <= 300)]),
                "high_fee_cards": len(df[df['annual_fee'] > 300])
            },
            "rewards_stats": {
                "cards_with_rewards": len(df[df['has_rewards'] == True]),
                "cards_with_signup_bonus": len(df[df['has_signup_bonus'] == True])
            },
            "last_updated": datetime.now().isoformat()
        }

        return stats

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting overview: {str(e)}")

@app.get("/api/cards/compare")
async def compare_cards(
    card_names: List[str] = Query(..., description="List of card names to compare")
) -> Dict:
    """Compare multiple credit cards side by side"""

    if not ai_engine:
        raise HTTPException(status_code=503, detail="AI engine not available")

    try:
        df = ai_engine.profiles_df

        # Find cards
        comparison_data = []
        for card_name in card_names:
            card_data = df[df['card_name'] == card_name]
            if not card_data.empty:
                card = card_data.iloc[0]
                comparison_data.append({
                    "card_name": card['card_name'],
                    "issuer": card['issuer'],
                    "annual_fee": card['annual_fee'],
                    "card_type": card['card_type'],
                    "has_rewards": card['has_rewards'],
                    "reward_categories": card['reward_categories'],
                    "total_benefits": card['total_benefits'],
                    "has_signup_bonus": card['has_signup_bonus'],
                    "best_for": eval(card['best_for']) if card['best_for'] != '[]' else []
                })

        return {
            "comparison": comparison_data,
            "comparison_count": len(comparison_data),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error comparing cards: {str(e)}")

# Demo endpoint that matches the wireframe exactly
@app.post("/api/demo/alex-johnson")
async def demo_alex_johnson() -> Dict:
    """Demo endpoint showing Alex Johnson's personalized recommendations (from wireframe)"""

    if not ai_engine:
        raise HTTPException(status_code=503, detail="AI engine not available")

    # Alex Johnson's profile from wireframe
    alex_profile = {
        'spending_patterns': {
            'dining': 800,      # 30% of spending (like wireframe shows)
            'travel': 600,      # Summer vacation planned
            'groceries': 400,
            'gas': 200,
            'entertainment': 150,
            'shopping': 300
        },
        'credit_score_range': 'excellent',
        'preferences': ['travel', 'cashback'],
        'current_cards': ['Chase Sapphire', 'Citi Double Cash', 'Amex Gold']
    }

    try:
        # Generate Alex's personalized dashboard
        dashboard = ai_engine.create_personalized_dashboard(alex_profile)

        # Format like the wireframe
        return {
            "user": "Alex Johnson",
            "total_monthly_spending": 2450,
            "spending_breakdown": dashboard['spending_insights']['category_breakdown'],
            "optimization_opportunities": dashboard['spending_insights']['optimization_opportunities'],
            "best_cards_by_category": dashboard['best_cards_by_category'],
            "new_card_recommendations": dashboard['new_card_recommendations'][:3],
            "optimization_tips": dashboard['optimization_tips'],
            "potential_annual_savings": sum(
                opp['annual_potential'] for opp in dashboard['spending_insights']['optimization_opportunities']
            )
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating demo: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting StackEasy API Server")
    print("🎯 Connecting AI recommendations to frontend")
    print("📡 API will be available at: http://localhost:8000")
    print("📚 API docs at: http://localhost:8000/docs")

    uvicorn.run(app, host="0.0.0.0", port=8000)
