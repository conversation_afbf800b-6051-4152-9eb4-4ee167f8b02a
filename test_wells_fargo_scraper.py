#!/usr/bin/env python3
"""
Test Wells Fargo Cards Scraping - Comprehensive Testing
Tests our advanced scraper on Wells Fargo credit cards
"""

import pandas as pd
from advanced_credit_card_scraper import AdvancedCreditCardScraper
import time
import json

def test_wells_fargo_cards():
    """Test scraper specifically on Wells Fargo cards"""
    
    # Load the CSV and filter for Wells Fargo cards
    df = pd.read_csv('credit_cards_validated.csv')
    wells_fargo_cards = df[df['Issuer Name'] == 'Wells Fargo'].copy()
    
    print(f"🏦 Found {len(wells_fargo_cards)} Wells Fargo cards to test")
    print("\n📋 Wells Fargo Cards List:")
    for i, (_, row) in enumerate(wells_fargo_cards.iterrows(), 1):
        print(f"   {i}. {row['Card Name']}")
    
    # Initialize scraper
    scraper = AdvancedCreditCardScraper()
    
    # Test with first 5 Wells Fargo cards to get a good sample
    test_cards = wells_fargo_cards.head(5)
    
    print(f"\n🧪 Testing with first 5 Wells Fargo cards...")
    print("=" * 70)
    
    results = []
    
    try:
        for i, (_, row) in enumerate(test_cards.iterrows(), 1):
            print(f"\n🔍 [{i}/5] Testing: {row['Card Name']}")
            print(f"    URL: {row['Card URL']}")
            
            # Scrape the card
            card_data = scraper.scrape_card_advanced(
                row['Issuer Name'], 
                row['Card Name'], 
                row['Card URL'],
                force_selenium=True  # Use Selenium for Wells Fargo
            )
            
            if card_data:
                results.append(card_data)
                
                # Display results immediately
                print(f"\n✅ Results for {card_data['card_name']}:")
                print(f"   Method: {card_data.get('scraping_method', 'unknown')}")
                print(f"   Annual Fee: {card_data.get('annual_fee', 'Not found')}")
                print(f"   Rewards: {len(card_data.get('rewards_rate', {}))} categories found")
                for category, rate in list(card_data.get('rewards_rate', {}).items())[:3]:
                    print(f"     • {rate} for {category[:50]}...")
                print(f"   Signup Bonus: {card_data.get('signup_bonus', 'Not found')}")
                print(f"   APR: {card_data.get('apr', 'Not found')}")
                print(f"   Benefits: {len(card_data.get('benefits', []))} found")
                for benefit in card_data.get('benefits', [])[:3]:
                    print(f"     • {benefit['type']}: {benefit['matched_text']}")
                
            else:
                print(f"❌ Failed to scrape {row['Card Name']}")
            
            # Be polite - wait between requests
            print("⏳ Waiting 3 seconds...")
            time.sleep(3)
    
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
    finally:
        scraper.cleanup()
    
    # Save results
    if results:
        filename = 'wells_fargo_test_results.json'
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\n💾 Saved {len(results)} Wells Fargo card results to {filename}")
        
        # Summary
        print(f"\n📊 WELLS FARGO TEST SUMMARY:")
        print(f"   Cards tested: {len(results)}")
        print(f"   Success rate: {len(results)}/{len(test_cards)} ({len(results)/len(test_cards)*100:.1f}%)")
        
        # Quick analysis
        fees_found = sum(1 for r in results if r.get('annual_fee'))
        rewards_found = sum(1 for r in results if r.get('rewards_rate'))
        bonuses_found = sum(1 for r in results if r.get('signup_bonus'))
        benefits_found = sum(len(r.get('benefits', [])) for r in results)
        apr_found = sum(1 for r in results if r.get('apr'))
        
        print(f"   Annual fees extracted: {fees_found}/{len(results)}")
        print(f"   Rewards rates extracted: {rewards_found}/{len(results)}")
        print(f"   Signup bonuses extracted: {bonuses_found}/{len(results)}")
        print(f"   APR rates extracted: {apr_found}/{len(results)}")
        print(f"   Total benefits extracted: {benefits_found}")
        
        if benefits_found > 0:
            avg_benefits = benefits_found / len(results)
            print(f"   Average benefits per card: {avg_benefits:.1f}")
    
    return results

def analyze_wells_fargo_vs_others():
    """Compare Wells Fargo results with our previous results"""
    try:
        # Load Wells Fargo results
        with open('wells_fargo_test_results.json', 'r') as f:
            wells_fargo_results = json.load(f)
        
        # Load other bank results
        with open('bofa_test_results.json', 'r') as f:
            bofa_results = json.load(f)
        
        with open('citi_test_results.json', 'r') as f:
            citi_results = json.load(f)
        
        with open('advanced_test_results.json', 'r') as f:
            amex_results = json.load(f)
        
        print(f"\n🔍 COMPARISON: Wells Fargo vs All Other Banks")
        print("=" * 80)
        
        print(f"📊 Data Extraction Comparison:")
        print(f"   Wells Fargo Cards Tested: {len(wells_fargo_results)}")
        print(f"   Bank of America Cards Tested: {len(bofa_results)}")
        print(f"   Citi Cards Tested: {len(citi_results)}")
        print(f"   Amex Cards Tested: {len(amex_results)}")
        
        # Compare extraction success rates
        banks_data = {
            'Wells Fargo': wells_fargo_results,
            'Bank of America': bofa_results,
            'Citi': citi_results,
            'American Express': amex_results
        }
        
        print(f"\n📈 Success Rates Comparison:")
        
        # Annual Fees
        print(f"   Annual Fees:")
        for bank_name, results in banks_data.items():
            fees = sum(1 for r in results if r.get('annual_fee'))
            rate = fees/len(results)*100 if results else 0
            print(f"     • {bank_name}: {fees}/{len(results)} ({rate:.1f}%)")
        
        # Rewards
        print(f"   Rewards Rates:")
        for bank_name, results in banks_data.items():
            rewards = sum(1 for r in results if r.get('rewards_rate'))
            rate = rewards/len(results)*100 if results else 0
            print(f"     • {bank_name}: {rewards}/{len(results)} ({rate:.1f}%)")
        
        # APR
        print(f"   APR Rates:")
        for bank_name, results in banks_data.items():
            apr = sum(1 for r in results if r.get('apr'))
            rate = apr/len(results)*100 if results else 0
            print(f"     • {bank_name}: {apr}/{len(results)} ({rate:.1f}%)")
        
        # Signup Bonuses
        print(f"   Signup Bonuses:")
        for bank_name, results in banks_data.items():
            bonuses = sum(1 for r in results if r.get('signup_bonus'))
            rate = bonuses/len(results)*100 if results else 0
            print(f"     • {bank_name}: {bonuses}/{len(results)} ({rate:.1f}%)")
        
        # Benefits
        print(f"   Benefits (Total):")
        for bank_name, results in banks_data.items():
            benefits = sum(len(r.get('benefits', [])) for r in results)
            print(f"     • {bank_name}: {benefits} total")
        
        print(f"   Benefits (Average per Card):")
        for bank_name, results in banks_data.items():
            if results:
                benefits = sum(len(r.get('benefits', [])) for r in results)
                avg_benefits = benefits / len(results)
                print(f"     • {bank_name}: {avg_benefits:.1f}")
        
        # Overall assessment
        print(f"\n🎯 OVERALL ASSESSMENT:")
        total_cards = sum(len(results) for results in banks_data.values())
        total_benefits = sum(sum(len(r.get('benefits', [])) for r in results) for results in banks_data.values())
        avg_benefits_overall = total_benefits / total_cards if total_cards > 0 else 0
        
        print(f"   Total cards tested across all banks: {total_cards}")
        print(f"   Total benefits extracted: {total_benefits}")
        print(f"   Overall average benefits per card: {avg_benefits_overall:.1f}")
        
        # Success rate across all banks
        successful_cards = sum(len(results) for results in banks_data.values())
        print(f"   Cross-bank compatibility: ✅ EXCELLENT ({successful_cards} cards)")
        
    except FileNotFoundError as e:
        print(f"⚠️ Could not load results for comparison: {e}")

def show_detailed_wells_fargo_results():
    """Show detailed results for each Wells Fargo card"""
    try:
        with open('wells_fargo_test_results.json', 'r') as f:
            wells_fargo_results = json.load(f)
        
        print(f"\n📋 DETAILED WELLS FARGO RESULTS:")
        print("=" * 70)
        
        for i, card in enumerate(wells_fargo_results, 1):
            print(f"\n🏦 [{i}] {card['card_name']}")
            print(f"   Annual Fee: {card.get('annual_fee', 'Not found')}")
            print(f"   APR: {card.get('apr', 'Not found')}")
            print(f"   Signup Bonus: {card.get('signup_bonus', 'Not found')}")
            
            rewards = card.get('rewards_rate', {})
            if rewards:
                print(f"   Rewards ({len(rewards)} categories):")
                for category, rate in rewards.items():
                    print(f"     • {rate} for {category[:60]}...")
            else:
                print(f"   Rewards: None found")
            
            benefits = card.get('benefits', [])
            if benefits:
                print(f"   Benefits ({len(benefits)} found):")
                for benefit in benefits[:5]:  # Show first 5 benefits
                    print(f"     • {benefit['type']}: {benefit['matched_text'][:50]}...")
            else:
                print(f"   Benefits: None found")
            
            print(f"   Scraping Method: {card.get('scraping_method', 'unknown')}")
            
            # Show meta description for context
            meta_desc = card.get('meta_description', '')
            if meta_desc:
                print(f"   Meta Description: {meta_desc[:100]}...")
    
    except FileNotFoundError:
        print("⚠️ Wells Fargo results file not found. Run the test first.")

def create_comprehensive_bank_summary():
    """Create a comprehensive summary of all bank testing"""
    try:
        # Load all results
        banks_files = {
            'Wells Fargo': 'wells_fargo_test_results.json',
            'Bank of America': 'bofa_test_results.json', 
            'Citi': 'citi_test_results.json',
            'American Express': 'advanced_test_results.json'
        }
        
        all_results = {}
        for bank_name, filename in banks_files.items():
            try:
                with open(filename, 'r') as f:
                    all_results[bank_name] = json.load(f)
            except FileNotFoundError:
                print(f"⚠️ {filename} not found, skipping {bank_name}")
        
        if not all_results:
            print("⚠️ No bank results found")
            return
        
        print(f"\n🏆 COMPREHENSIVE BANK TESTING SUMMARY")
        print("=" * 80)
        
        # Create summary table
        print(f"{'Bank':<20} {'Cards':<8} {'Fees':<8} {'Rewards':<10} {'APR':<8} {'Bonuses':<10} {'Benefits':<10}")
        print("-" * 80)
        
        total_cards = 0
        total_benefits = 0
        
        for bank_name, results in all_results.items():
            if not results:
                continue
                
            cards = len(results)
            fees = sum(1 for r in results if r.get('annual_fee'))
            rewards = sum(1 for r in results if r.get('rewards_rate'))
            apr = sum(1 for r in results if r.get('apr'))
            bonuses = sum(1 for r in results if r.get('signup_bonus'))
            benefits = sum(len(r.get('benefits', [])) for r in results)
            
            total_cards += cards
            total_benefits += benefits
            
            print(f"{bank_name:<20} {cards:<8} {fees:<8} {rewards:<10} {apr:<8} {bonuses:<10} {benefits:<10}")
        
        print("-" * 80)
        print(f"{'TOTAL':<20} {total_cards:<8} {'':<8} {'':<10} {'':<8} {'':<10} {total_benefits:<10}")
        
        if total_cards > 0:
            avg_benefits = total_benefits / total_cards
            print(f"\n📊 Overall Statistics:")
            print(f"   Total cards successfully scraped: {total_cards}")
            print(f"   Total benefits extracted: {total_benefits}")
            print(f"   Average benefits per card: {avg_benefits:.1f}")
            print(f"   Banks tested: {len(all_results)}")
            print(f"   Cross-bank compatibility: ✅ PROVEN")
        
    except Exception as e:
        print(f"❌ Error creating summary: {e}")

if __name__ == "__main__":
    print("🚀 Starting Wells Fargo Cards Scraping Test")
    print("🎯 Goal: Test our scraper on Wells Fargo (19 cards total)")
    print("📊 This will help us understand Wells Fargo's website structure")
    print()
    
    # Test Wells Fargo cards
    results = test_wells_fargo_cards()
    
    # Show detailed results
    if results:
        show_detailed_wells_fargo_results()
        
        # Compare with other banks
        analyze_wells_fargo_vs_others()
        
        # Create comprehensive summary
        create_comprehensive_bank_summary()
    
    print("\n✅ Wells Fargo testing complete!")
    print("💡 Next: Based on results, we can test U.S. Bank or run full scale scraping")
