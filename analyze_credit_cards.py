#!/usr/bin/env python3
"""
StackEasy Credit Card Data Analysis
Clean, analyze, and prepare our scraped credit card data for AI recommendations
"""

import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter, defaultdict
import re
from datetime import datetime

class CreditCardAnalyzer:
    def __init__(self, data_file: str):
        """Initialize analyzer with scraped credit card data"""
        self.data_file = data_file
        self.raw_data = None
        self.clean_data = None
        self.analysis_results = {}
        
        # Load the data
        self.load_data()
        
    def load_data(self):
        """Load scraped credit card data"""
        try:
            with open(self.data_file, 'r') as f:
                self.raw_data = json.load(f)
            print(f"✅ Loaded {len(self.raw_data)} credit cards from {self.data_file}")
        except FileNotFoundError:
            print(f"❌ Error: {self.data_file} not found")
            return False
        except json.JSONDecodeError:
            print(f"❌ Error: Invalid JSON in {self.data_file}")
            return False
        
        return True
    
    def explore_data_structure(self):
        """Explore the structure of our scraped data"""
        print("\n🔍 DATA STRUCTURE EXPLORATION")
        print("=" * 60)
        
        if not self.raw_data:
            print("❌ No data loaded")
            return
        
        # Sample card structure
        sample_card = self.raw_data[0]
        print(f"📋 Sample Card Structure:")
        for key, value in sample_card.items():
            if isinstance(value, list) and len(value) > 0:
                print(f"   {key}: {type(value).__name__} with {len(value)} items")
                if key == 'benefits':
                    print(f"      Sample benefit: {value[0]}")
            elif isinstance(value, dict) and len(value) > 0:
                print(f"   {key}: {type(value).__name__} with {len(value)} items")
                print(f"      Sample: {list(value.items())[0]}")
            else:
                print(f"   {key}: {value}")
        
        # Data completeness analysis
        print(f"\n📊 DATA COMPLETENESS:")
        fields = ['card_name', 'issuer', 'annual_fee', 'rewards_rate', 'signup_bonus', 'apr', 'benefits']
        
        for field in fields:
            count = sum(1 for card in self.raw_data if card.get(field))
            percentage = count / len(self.raw_data) * 100
            print(f"   {field}: {count}/{len(self.raw_data)} ({percentage:.1f}%)")
        
        # Benefits analysis
        total_benefits = sum(len(card.get('benefits', [])) for card in self.raw_data)
        avg_benefits = total_benefits / len(self.raw_data)
        print(f"\n🎁 BENEFITS SUMMARY:")
        print(f"   Total benefits: {total_benefits}")
        print(f"   Average per card: {avg_benefits:.1f}")
        
        return {
            'total_cards': len(self.raw_data),
            'total_benefits': total_benefits,
            'avg_benefits': avg_benefits,
            'completeness': {field: sum(1 for card in self.raw_data if card.get(field)) for field in fields}
        }
    
    def clean_annual_fees(self):
        """Clean and standardize annual fee data"""
        print("\n💰 CLEANING ANNUAL FEES")
        print("=" * 40)
        
        fee_patterns = []
        cleaned_fees = []
        
        for card in self.raw_data:
            fee_raw = card.get('annual_fee', '')
            fee_cleaned = self.extract_fee_amount(fee_raw)
            
            fee_patterns.append(fee_raw)
            cleaned_fees.append(fee_cleaned)
            
            card['annual_fee_cleaned'] = fee_cleaned
        
        # Analyze fee patterns
        unique_patterns = Counter(fee_patterns)
        print(f"📋 Fee Patterns Found:")
        for pattern, count in unique_patterns.most_common(10):
            print(f"   '{pattern}': {count} cards")
        
        # Fee distribution
        fee_distribution = Counter(cleaned_fees)
        print(f"\n📊 Fee Distribution:")
        for fee, count in sorted(fee_distribution.items()):
            print(f"   ${fee}: {count} cards")
        
        return cleaned_fees
    
    def extract_fee_amount(self, fee_text: str) -> int:
        """Extract numeric fee amount from text"""
        if not fee_text or fee_text.lower() in ['not found', 'none', '']:
            return 0
        
        # Remove common prefixes/suffixes
        fee_text = str(fee_text).lower().replace('$', '').replace(',', '')
        
        # Look for numeric patterns
        numbers = re.findall(r'\d+', fee_text)
        
        if not numbers:
            return 0
        
        # Take the first number found
        return int(numbers[0])
    
    def analyze_rewards_structure(self):
        """Analyze reward rate structures across cards"""
        print("\n🎁 ANALYZING REWARDS STRUCTURES")
        print("=" * 50)
        
        reward_categories = defaultdict(list)
        cards_with_rewards = 0
        
        for card in self.raw_data:
            rewards = card.get('rewards_rate', {})
            if rewards:
                cards_with_rewards += 1
                for category, rate in rewards.items():
                    reward_categories[category.lower()].append({
                        'card': card['card_name'],
                        'issuer': card['issuer'],
                        'rate': rate,
                        'category': category
                    })
        
        print(f"📊 Cards with rewards: {cards_with_rewards}/{len(self.raw_data)}")
        print(f"📋 Reward categories found: {len(reward_categories)}")
        
        # Top reward categories
        print(f"\n🏆 TOP REWARD CATEGORIES:")
        sorted_categories = sorted(reward_categories.items(), key=lambda x: len(x[1]), reverse=True)
        
        for category, cards in sorted_categories[:10]:
            print(f"   {category}: {len(cards)} cards")
            # Show best rates for this category
            rates = [self.extract_reward_rate(card['rate']) for card in cards]
            rates = [r for r in rates if r > 0]
            if rates:
                max_rate = max(rates)
                best_cards = [card for card in cards if self.extract_reward_rate(card['rate']) == max_rate]
                print(f"      Best rate: {max_rate}x ({best_cards[0]['card']})")
        
        return reward_categories
    
    def extract_reward_rate(self, rate_text: str) -> float:
        """Extract numeric reward rate from text"""
        if not rate_text:
            return 0.0
        
        # Look for patterns like "2x", "3%", "2 points", etc.
        rate_text = str(rate_text).lower()
        
        # Extract numbers
        numbers = re.findall(r'(\d+(?:\.\d+)?)', rate_text)
        
        if not numbers:
            return 0.0
        
        return float(numbers[0])
    
    def analyze_benefits_categories(self):
        """Analyze and categorize card benefits"""
        print("\n✨ ANALYZING BENEFITS CATEGORIES")
        print("=" * 50)
        
        benefit_types = defaultdict(list)
        all_benefits = []
        
        for card in self.raw_data:
            benefits = card.get('benefits', [])
            for benefit in benefits:
                benefit_type = benefit.get('type', 'unknown')
                benefit_text = benefit.get('matched_text', '')
                
                benefit_types[benefit_type].append({
                    'card': card['card_name'],
                    'issuer': card['issuer'],
                    'text': benefit_text,
                    'type': benefit_type
                })
                all_benefits.append(benefit)
        
        print(f"📊 Total benefits analyzed: {len(all_benefits)}")
        print(f"📋 Benefit types found: {len(benefit_types)}")
        
        # Top benefit types
        print(f"\n🏆 TOP BENEFIT TYPES:")
        sorted_types = sorted(benefit_types.items(), key=lambda x: len(x[1]), reverse=True)
        
        for benefit_type, benefits in sorted_types[:15]:
            print(f"   {benefit_type}: {len(benefits)} benefits")
            
            # Show which issuers offer this benefit most
            issuers = [b['issuer'] for b in benefits]
            top_issuer = Counter(issuers).most_common(1)[0]
            print(f"      Top issuer: {top_issuer[0]} ({top_issuer[1]} cards)")
        
        return benefit_types
    
    def create_card_profiles(self):
        """Create comprehensive profiles for each card"""
        print("\n📋 CREATING CARD PROFILES")
        print("=" * 40)
        
        profiles = []
        
        for card in self.raw_data:
            profile = {
                'card_name': card['card_name'],
                'issuer': card['issuer'],
                'annual_fee': card.get('annual_fee_cleaned', 0),
                'has_rewards': bool(card.get('rewards_rate')),
                'reward_categories': len(card.get('rewards_rate', {})),
                'total_benefits': len(card.get('benefits', [])),
                'has_signup_bonus': bool(card.get('signup_bonus')),
                'has_apr': bool(card.get('apr')),
                'card_type': self.classify_card_type(card),
                'best_for': self.identify_best_categories(card)
            }
            profiles.append(profile)
        
        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(profiles)
        
        print(f"✅ Created profiles for {len(profiles)} cards")
        
        # Quick analysis
        print(f"\n📊 CARD TYPE DISTRIBUTION:")
        type_counts = df['card_type'].value_counts()
        for card_type, count in type_counts.items():
            print(f"   {card_type}: {count} cards")
        
        print(f"\n💰 FEE DISTRIBUTION:")
        fee_ranges = pd.cut(df['annual_fee'], bins=[0, 1, 100, 300, 500, 1000], labels=['Free', '$1-100', '$101-300', '$301-500', '$500+'])
        fee_counts = fee_ranges.value_counts()
        for fee_range, count in fee_counts.items():
            print(f"   {fee_range}: {count} cards")
        
        return df
    
    def classify_card_type(self, card):
        """Classify card type based on features"""
        card_name = card['card_name'].lower()
        benefits = [b.get('type', '').lower() for b in card.get('benefits', [])]
        rewards = list(card.get('rewards_rate', {}).keys())
        
        # Business cards
        if 'business' in card_name:
            return 'Business'
        
        # Travel cards
        travel_indicators = ['travel', 'airline', 'airport', 'lounge', 'miles', 'points']
        if any(indicator in card_name for indicator in travel_indicators):
            return 'Travel'
        if any(indicator in ' '.join(benefits) for indicator in travel_indicators):
            return 'Travel'
        
        # Cash back cards
        cashback_indicators = ['cash', 'cashback', 'double cash']
        if any(indicator in card_name for indicator in cashback_indicators):
            return 'Cashback'
        
        # Dining/Entertainment cards
        if 'gold' in card_name or 'dining' in card_name:
            return 'Dining/Entertainment'
        
        # Store cards
        store_indicators = ['amazon', 'costco', 'target', 'walmart']
        if any(indicator in card_name for indicator in store_indicators):
            return 'Store/Retail'
        
        # Default
        return 'General'
    
    def identify_best_categories(self, card):
        """Identify what spending categories this card is best for"""
        rewards = card.get('rewards_rate', {})
        best_categories = []
        
        for category, rate in rewards.items():
            rate_num = self.extract_reward_rate(rate)
            if rate_num >= 2.0:  # 2x or higher
                best_categories.append(category.lower())
        
        return best_categories
    
    def generate_summary_report(self):
        """Generate comprehensive summary report"""
        print("\n📊 GENERATING SUMMARY REPORT")
        print("=" * 50)
        
        # Run all analyses
        structure_info = self.explore_data_structure()
        self.clean_annual_fees()
        reward_categories = self.analyze_rewards_structure()
        benefit_types = self.analyze_benefits_categories()
        card_profiles_df = self.create_card_profiles()
        
        # Create summary
        summary = {
            'analysis_date': datetime.now().isoformat(),
            'total_cards': len(self.raw_data),
            'data_quality': structure_info,
            'top_reward_categories': list(sorted(reward_categories.keys(), key=lambda x: len(reward_categories[x]), reverse=True)[:10]),
            'top_benefit_types': list(sorted(benefit_types.keys(), key=lambda x: len(benefit_types[x]), reverse=True)[:10]),
            'card_type_distribution': card_profiles_df['card_type'].value_counts().to_dict(),
            'issuer_distribution': card_profiles_df['issuer'].value_counts().to_dict()
        }
        
        # Save summary
        with open('credit_card_analysis_summary.json', 'w') as f:
            json.dump(summary, f, indent=2)
        
        # Save cleaned data
        card_profiles_df.to_csv('credit_card_profiles.csv', index=False)
        
        print(f"\n✅ ANALYSIS COMPLETE!")
        print(f"📁 Saved summary to: credit_card_analysis_summary.json")
        print(f"📁 Saved profiles to: credit_card_profiles.csv")
        
        return summary, card_profiles_df

def main():
    """Main analysis function"""
    print("🚀 STACKEASY CREDIT CARD DATA ANALYSIS")
    print("🎯 Goal: Clean and analyze our 94-card dataset for AI recommendations")
    print("=" * 70)
    
    # Find the latest results file
    import glob
    result_files = glob.glob('scraping_results_*/all_cards_results_*.json')
    
    if not result_files:
        print("❌ No scraping results found. Please run the scraper first.")
        return
    
    # Use the most recent file
    latest_file = max(result_files)
    print(f"📁 Using data file: {latest_file}")
    
    # Initialize analyzer
    analyzer = CreditCardAnalyzer(latest_file)
    
    # Run comprehensive analysis
    summary, profiles_df = analyzer.generate_summary_report()
    
    print(f"\n🎉 READY FOR NEXT PHASE!")
    print(f"💡 Next: Build AI recommendation algorithms using this clean data")

if __name__ == "__main__":
    main()
