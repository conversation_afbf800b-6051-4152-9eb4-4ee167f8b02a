#!/usr/bin/env python3
"""
Scale All Banks Credit Card Scraper - Production Version
Scrapes all 97 credit cards across all banks with robust error handling
"""

import pandas as pd
from advanced_credit_card_scraper import AdvancedCreditCardScraper
import time
import json
import os
from datetime import datetime
import logging

# Set up detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraping_log.txt'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductionCreditCardScraper:
    def __init__(self, csv_file: str = 'credit_cards_validated.csv'):
        """Initialize production scraper with comprehensive error handling"""
        self.csv_file = csv_file
        self.cards_df = pd.read_csv(csv_file)
        self.scraper = AdvancedCreditCardScraper()
        self.results = []
        self.failed_cards = []
        self.start_time = datetime.now()
        
        # Create results directory
        self.results_dir = f"scraping_results_{self.start_time.strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(self.results_dir, exist_ok=True)
        
        logger.info(f"Initialized scraper for {len(self.cards_df)} cards")
        logger.info(f"Results will be saved to: {self.results_dir}")
    
    def analyze_card_distribution(self):
        """Analyze the distribution of cards by issuer"""
        issuer_counts = self.cards_df['Issuer Name'].value_counts()
        
        print(f"\n📊 CARD DISTRIBUTION BY ISSUER:")
        print("=" * 50)
        for issuer, count in issuer_counts.items():
            print(f"   {issuer}: {count} cards")
        
        print(f"\n📈 TOTAL: {len(self.cards_df)} cards across {len(issuer_counts)} issuers")
        return issuer_counts
    
    def scrape_by_issuer(self, issuer: str, max_cards: int = None):
        """Scrape all cards from a specific issuer"""
        issuer_cards = self.cards_df[self.cards_df['Issuer Name'] == issuer].copy()
        
        if max_cards:
            issuer_cards = issuer_cards.head(max_cards)
        
        logger.info(f"Starting {issuer} scraping: {len(issuer_cards)} cards")
        print(f"\n🏦 SCRAPING {issuer.upper()} CARDS")
        print("=" * 60)
        
        issuer_results = []
        issuer_failures = []
        
        for i, (_, row) in enumerate(issuer_cards.iterrows(), 1):
            try:
                print(f"\n🔍 [{i}/{len(issuer_cards)}] {row['Card Name']}")
                
                # Scrape the card
                card_data = self.scraper.scrape_card_advanced(
                    row['Issuer Name'], 
                    row['Card Name'], 
                    row['Card URL'],
                    force_selenium=True
                )
                
                if card_data:
                    issuer_results.append(card_data)
                    self.results.append(card_data)
                    
                    # Quick summary
                    print(f"   ✅ Success: {card_data.get('annual_fee', 'N/A')} fee, "
                          f"{len(card_data.get('rewards_rate', {}))} rewards, "
                          f"{len(card_data.get('benefits', []))} benefits")
                else:
                    issuer_failures.append({
                        'issuer': row['Issuer Name'],
                        'card_name': row['Card Name'],
                        'url': row['Card URL'],
                        'error': 'No data returned'
                    })
                    self.failed_cards.append(issuer_failures[-1])
                    print(f"   ❌ Failed: No data returned")
                
                # Save progress after each card
                self.save_progress()
                
                # Be polite - wait between requests
                time.sleep(3)
                
            except Exception as e:
                error_info = {
                    'issuer': row['Issuer Name'],
                    'card_name': row['Card Name'],
                    'url': row['Card URL'],
                    'error': str(e)
                }
                issuer_failures.append(error_info)
                self.failed_cards.append(error_info)
                logger.error(f"Error scraping {row['Card Name']}: {e}")
                print(f"   ❌ Error: {str(e)[:50]}...")
        
        # Issuer summary
        success_rate = len(issuer_results) / len(issuer_cards) * 100
        print(f"\n📊 {issuer.upper()} SUMMARY:")
        print(f"   Success: {len(issuer_results)}/{len(issuer_cards)} ({success_rate:.1f}%)")
        print(f"   Failed: {len(issuer_failures)}")
        
        if issuer_results:
            avg_benefits = sum(len(r.get('benefits', [])) for r in issuer_results) / len(issuer_results)
            print(f"   Avg benefits per card: {avg_benefits:.1f}")
        
        return issuer_results, issuer_failures
    
    def scrape_all_cards(self, start_from_issuer: str = None):
        """Scrape all cards from all issuers"""
        issuer_counts = self.analyze_card_distribution()
        
        print(f"\n🚀 STARTING FULL SCALE SCRAPING")
        print(f"📅 Started at: {self.start_time}")
        print("=" * 60)
        
        issuers = list(issuer_counts.index)
        
        # Start from specific issuer if requested
        if start_from_issuer and start_from_issuer in issuers:
            start_idx = issuers.index(start_from_issuer)
            issuers = issuers[start_idx:]
            print(f"🔄 Resuming from: {start_from_issuer}")
        
        try:
            for issuer in issuers:
                self.scrape_by_issuer(issuer)
                
                # Progress update
                total_scraped = len(self.results)
                total_cards = len(self.cards_df)
                progress = total_scraped / total_cards * 100
                
                print(f"\n📈 OVERALL PROGRESS: {total_scraped}/{total_cards} ({progress:.1f}%)")
                
                # Save comprehensive results after each issuer
                self.save_comprehensive_results()
                
        except KeyboardInterrupt:
            print(f"\n⏹️ Scraping interrupted by user")
            logger.info("Scraping interrupted by user")
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")
            logger.error(f"Unexpected error: {e}")
        finally:
            self.cleanup_and_finalize()
    
    def save_progress(self):
        """Save current progress"""
        progress_file = os.path.join(self.results_dir, 'progress.json')
        progress_data = {
            'scraped_count': len(self.results),
            'total_count': len(self.cards_df),
            'failed_count': len(self.failed_cards),
            'last_updated': datetime.now().isoformat()
        }
        
        with open(progress_file, 'w') as f:
            json.dump(progress_data, f, indent=2)
    
    def save_comprehensive_results(self):
        """Save comprehensive results with analysis"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Save main results
        results_file = os.path.join(self.results_dir, f'all_cards_results_{timestamp}.json')
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        # Save failures
        if self.failed_cards:
            failures_file = os.path.join(self.results_dir, f'failed_cards_{timestamp}.json')
            with open(failures_file, 'w') as f:
                json.dump(self.failed_cards, f, indent=2)
        
        # Create summary analysis
        self.create_summary_analysis(timestamp)
        
        logger.info(f"Saved {len(self.results)} results and {len(self.failed_cards)} failures")
    
    def create_summary_analysis(self, timestamp: str):
        """Create comprehensive summary analysis"""
        if not self.results:
            return
        
        analysis = {
            'scraping_summary': {
                'total_cards_attempted': len(self.results) + len(self.failed_cards),
                'successful_extractions': len(self.results),
                'failed_extractions': len(self.failed_cards),
                'success_rate': len(self.results) / (len(self.results) + len(self.failed_cards)) * 100,
                'scraping_duration': str(datetime.now() - self.start_time)
            },
            'data_quality': {
                'cards_with_annual_fee': sum(1 for r in self.results if r.get('annual_fee')),
                'cards_with_rewards': sum(1 for r in self.results if r.get('rewards_rate')),
                'cards_with_signup_bonus': sum(1 for r in self.results if r.get('signup_bonus')),
                'cards_with_benefits': sum(1 for r in self.results if r.get('benefits')),
                'total_benefits_extracted': sum(len(r.get('benefits', [])) for r in self.results),
                'avg_benefits_per_card': sum(len(r.get('benefits', [])) for r in self.results) / len(self.results)
            },
            'issuer_breakdown': {}
        }
        
        # Issuer breakdown
        for result in self.results:
            issuer = result.get('issuer', 'Unknown')
            if issuer not in analysis['issuer_breakdown']:
                analysis['issuer_breakdown'][issuer] = {
                    'cards_scraped': 0,
                    'avg_benefits': 0,
                    'cards_with_fees': 0,
                    'cards_with_rewards': 0
                }
            
            breakdown = analysis['issuer_breakdown'][issuer]
            breakdown['cards_scraped'] += 1
            breakdown['avg_benefits'] += len(result.get('benefits', []))
            if result.get('annual_fee'):
                breakdown['cards_with_fees'] += 1
            if result.get('rewards_rate'):
                breakdown['cards_with_rewards'] += 1
        
        # Calculate averages
        for issuer_data in analysis['issuer_breakdown'].values():
            if issuer_data['cards_scraped'] > 0:
                issuer_data['avg_benefits'] /= issuer_data['cards_scraped']
        
        # Save analysis
        analysis_file = os.path.join(self.results_dir, f'scraping_analysis_{timestamp}.json')
        with open(analysis_file, 'w') as f:
            json.dump(analysis, f, indent=2)
        
        # Print summary
        print(f"\n📊 SCRAPING ANALYSIS SUMMARY:")
        print(f"   Success Rate: {analysis['scraping_summary']['success_rate']:.1f}%")
        print(f"   Total Benefits: {analysis['data_quality']['total_benefits_extracted']}")
        print(f"   Avg Benefits/Card: {analysis['data_quality']['avg_benefits_per_card']:.1f}")
    
    def cleanup_and_finalize(self):
        """Clean up resources and finalize results"""
        self.scraper.cleanup()
        self.save_comprehensive_results()
        
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        print(f"\n✅ SCRAPING COMPLETED!")
        print(f"📅 Duration: {duration}")
        print(f"📊 Results: {len(self.results)} successful, {len(self.failed_cards)} failed")
        print(f"💾 Saved to: {self.results_dir}")
        
        logger.info(f"Scraping completed. Duration: {duration}")

def main():
    """Main execution function"""
    print("🚀 STACKEASY CREDIT CARD SCRAPER - PRODUCTION SCALE")
    print("=" * 60)
    print("📊 This will scrape ALL 97 credit cards across all banks")
    print("⏱️  Estimated time: 5-8 minutes (3 seconds per card)")
    print("💾 Results will be saved with comprehensive analysis")
    print()
    
    # Initialize scraper
    scraper = ProductionCreditCardScraper()
    
    # Option to test specific issuer first
    test_mode = input("🧪 Test specific issuer first? (y/n): ").lower().strip()
    
    if test_mode == 'y':
        issuer_counts = scraper.analyze_card_distribution()
        print("\nAvailable issuers:")
        for i, issuer in enumerate(issuer_counts.index, 1):
            print(f"   {i}. {issuer} ({issuer_counts[issuer]} cards)")
        
        try:
            choice = int(input("\nSelect issuer number: ")) - 1
            selected_issuer = list(issuer_counts.index)[choice]
            max_cards = input(f"Max cards to test for {selected_issuer} (or press Enter for all): ")
            max_cards = int(max_cards) if max_cards.strip() else None
            
            scraper.scrape_by_issuer(selected_issuer, max_cards)
        except (ValueError, IndexError):
            print("Invalid selection. Starting full scraping...")
            scraper.scrape_all_cards()
    else:
        # Full scale scraping
        start_issuer = input("🔄 Start from specific issuer? (or press Enter to start from beginning): ").strip()
        start_issuer = start_issuer if start_issuer else None
        
        scraper.scrape_all_cards(start_from_issuer=start_issuer)

if __name__ == "__main__":
    main()
