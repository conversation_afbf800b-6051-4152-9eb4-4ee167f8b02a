#!/usr/bin/env python3
"""
Credit Card Web Scraper for StackEasy Project
Scrapes detailed credit card information from bank websites
"""

import pandas as pd
import requests
from bs4 import BeautifulSoup
import time
import json
import re
from urllib.parse import urljoin, urlparse
import logging
from typing import Dict, List, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CreditCardScraper:
    def __init__(self, csv_file: str = 'credit_cards_validated.csv'):
        """Initialize the scraper with the credit cards CSV file"""
        self.csv_file = csv_file
        self.cards_df = pd.read_csv(csv_file)
        self.scraped_data = []

        # Headers to mimic a real browser
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }

        # Session for connection pooling
        self.session = requests.Session()
        self.session.headers.update(self.headers)

    def get_page_content(self, url: str, debug: bool = False) -> Optional[BeautifulSoup]:
        """Fetch and parse webpage content"""
        try:
            logger.info(f"Fetching: {url}")
            response = self.session.get(url, timeout=15)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Debug: Save raw HTML for inspection
            if debug:
                domain = urlparse(url).netloc.replace('.', '_')
                debug_file = f"debug_{domain}.html"
                with open(debug_file, 'w', encoding='utf-8') as f:
                    f.write(str(soup.prettify()))
                logger.info(f"Debug HTML saved to {debug_file}")

            return soup

        except requests.exceptions.RequestException as e:
            logger.error(f"Error fetching {url}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error for {url}: {e}")
            return None

    def extract_amex_features(self, soup: BeautifulSoup, url: str) -> Dict:
        """Extract features from American Express card pages"""
        features = {
            'annual_fee': None,
            'rewards_rate': {},
            'signup_bonus': None,
            'apr': None,
            'benefits': [],
            'categories': [],
            'raw_text_sample': None,  # For debugging
            'meta_description': None  # Store meta description
        }

        try:
            # Extract meta description which often contains key info
            meta_desc = soup.find('meta', {'property': 'og:description'})
            if meta_desc:
                features['meta_description'] = meta_desc.get('content', '')
                logger.info(f"Found meta description: {features['meta_description'][:100]}...")

            # Get page text and save sample for debugging
            page_text = soup.get_text()
            features['raw_text_sample'] = page_text[:500]  # First 500 chars for debugging
            page_text_lower = page_text.lower()

            # Also check meta description for patterns
            search_text = page_text_lower
            if features['meta_description']:
                search_text += " " + features['meta_description'].lower()

            logger.info(f"Page text length: {len(page_text)} characters")

            # Look for annual fee - more comprehensive patterns
            fee_patterns = [
                r'\$(\d+)\s*annual\s*fee',
                r'annual\s*fee[:\s]*\$(\d+)',
                r'\$(\d+)\s*per\s*year',
                r'(\d+)\s*dollar\s*annual\s*fee',
                r'fee:\s*\$(\d+)',
                r'annual\s*membership\s*fee[:\s]*\$(\d+)'
            ]

            for pattern in fee_patterns:
                match = re.search(pattern, search_text)
                if match:
                    features['annual_fee'] = f"${match.group(1)}"
                    logger.info(f"Found annual fee: ${match.group(1)}")
                    break

            # Look for "no annual fee" variations
            no_fee_patterns = [
                r'no\s*annual\s*fee',
                r'\$0\s*annual\s*fee',
                r'zero\s*annual\s*fee',
                r'waived\s*annual\s*fee'
            ]

            for pattern in no_fee_patterns:
                if re.search(pattern, search_text):
                    features['annual_fee'] = "$0"
                    logger.info("Found no annual fee")
                    break

            # Look for rewards rates - expanded patterns optimized for meta descriptions
            rewards_patterns = [
                # Amex-specific patterns from meta descriptions
                r'earn\s*(\d+)x\s*(?:membership\s*rewards®?\s*)?points?\s*on\s*([^.!?,]+)',
                r'(\d+)x\s*(?:membership\s*rewards®?\s*)?points?\s*on\s*([^.!?,]+)',
                r'(\d+)\s*(?:membership\s*rewards®?\s*)?points?\s*per\s*\$1\s*(?:on|for|spent\s*on)\s*([^.!?,]+)',
                r'(\d+)%\s*cash\s*back\s*(?:on|for)\s*([^.!?,]+)',
                r'earn\s*(\d+)x\s*points?\s*(?:on|for)\s*([^.!?,]+)',
                r'(\d+)\s*points?\s*for\s*every\s*\$1\s*spent\s*(?:on|at)\s*([^.!?,]+)'
            ]

            for pattern in rewards_patterns:
                matches = re.finditer(pattern, search_text)
                for match in matches:
                    rate = match.group(1)
                    category = match.group(2).strip()

                    # Clean up category names
                    category = re.sub(r'\s+', ' ', category)  # Normalize whitespace
                    category = re.sub(r'[®™]', '', category)  # Remove trademark symbols
                    category = category.strip()[:50]  # Limit length

                    if len(category) > 3:  # Avoid very short matches
                        reward_type = f"{rate}x" if 'points' in pattern else f"{rate}%"
                        features['rewards_rate'][category] = reward_type
                        logger.info(f"Found reward: {reward_type} for {category}")

            # Look for signup bonus - expanded patterns
            bonus_patterns = [
                r'(\d+,?\d*)\s*(?:bonus\s*)?(?:membership\s*rewards\s*)?points?\s*after\s*(?:you\s*)?spend\s*\$(\d+,?\d*)',
                r'earn\s*(\d+,?\d*)\s*(?:bonus\s*)?points?\s*when\s*you\s*spend\s*\$(\d+,?\d*)',
                r'\$(\d+,?\d*)\s*statement\s*credit\s*after\s*(?:you\s*)?spend\s*\$(\d+,?\d*)',
                r'welcome\s*(?:offer|bonus)[:\s]*(\d+,?\d*)\s*points?\s*after\s*spending\s*\$(\d+,?\d*)',
                r'get\s*(\d+,?\d*)\s*points?\s*after\s*spending\s*\$(\d+,?\d*)'
            ]

            for pattern in bonus_patterns:
                match = re.search(pattern, search_text)
                if match:
                    if 'statement.*credit' in pattern:
                        features['signup_bonus'] = f"${match.group(1)} statement credit after ${match.group(2)} spend"
                    else:
                        features['signup_bonus'] = f"{match.group(1)} points after ${match.group(2)} spend"
                    logger.info(f"Found signup bonus: {features['signup_bonus']}")
                    break

        except Exception as e:
            logger.error(f"Error extracting Amex features from {url}: {e}")

        return features

    def extract_generic_features(self, soup: BeautifulSoup, url: str) -> Dict:
        """Generic feature extraction for any credit card page"""
        features = {
            'annual_fee': None,
            'rewards_rate': {},
            'signup_bonus': None,
            'apr': None,
            'benefits': [],
            'categories': []
        }

        try:
            page_text = soup.get_text().lower()

            # Extract annual fee
            if re.search(r'no\s*annual\s*fee', page_text):
                features['annual_fee'] = "$0"
            else:
                fee_match = re.search(r'\$(\d+)\s*annual\s*fee', page_text)
                if fee_match:
                    features['annual_fee'] = f"${fee_match.group(1)}"

            # Extract basic rewards info
            rewards_match = re.search(r'(\d+)%\s*cash\s*back', page_text)
            if rewards_match:
                features['rewards_rate']['general'] = f"{rewards_match.group(1)}%"

            points_match = re.search(r'(\d+)x?\s*points?', page_text)
            if points_match:
                features['rewards_rate']['general'] = f"{points_match.group(1)}x"

        except Exception as e:
            logger.error(f"Error extracting generic features from {url}: {e}")

        return features

    def scrape_card(self, issuer: str, card_name: str, url: str, debug: bool = False) -> Dict:
        """Scrape a single credit card"""
        logger.info(f"Scraping: {issuer} - {card_name}")

        soup = self.get_page_content(url, debug=debug)
        if not soup:
            return None

        # Choose extraction method based on issuer
        if 'american express' in issuer.lower() or 'amex' in issuer.lower():
            features = self.extract_amex_features(soup, url)
        else:
            features = self.extract_generic_features(soup, url)

        # Add basic card info
        card_data = {
            'issuer': issuer,
            'card_name': card_name,
            'url': url,
            'scraped_at': pd.Timestamp.now().isoformat(),
            **features
        }

        return card_data

    def scrape_sample_cards(self, num_cards: int = 5) -> List[Dict]:
        """Scrape a sample of cards for testing"""
        logger.info(f"Scraping sample of {num_cards} cards...")

        sample_cards = self.cards_df.head(num_cards)
        results = []

        for i, (_, row) in enumerate(sample_cards.iterrows()):
            # Enable debug for first card to see what we're getting
            debug = (i == 0)
            card_data = self.scrape_card(row['Issuer Name'], row['Card Name'], row['Card URL'], debug=debug)
            if card_data:
                results.append(card_data)
                self.scraped_data.append(card_data)

            # Be polite - wait between requests
            time.sleep(2)

        return results

    def save_results(self, filename: str = 'scraped_credit_cards.json'):
        """Save scraped data to JSON file"""
        with open(filename, 'w') as f:
            json.dump(self.scraped_data, f, indent=2)
        logger.info(f"Saved {len(self.scraped_data)} cards to {filename}")

if __name__ == "__main__":
    # Test the scraper with a few cards
    scraper = CreditCardScraper()

    print("🚀 Starting Credit Card Scraper Test...")
    print(f"📊 Total cards to scrape: {len(scraper.cards_df)}")
    print("🧪 Testing with first 3 cards...\n")

    # Test with first 3 cards
    results = scraper.scrape_sample_cards(3)

    print(f"\n✅ Successfully scraped {len(results)} cards")

    # Display results
    for card in results:
        print(f"\n📋 {card['issuer']} - {card['card_name']}")
        print(f"   Annual Fee: {card.get('annual_fee', 'Not found')}")
        print(f"   Rewards: {card.get('rewards_rate', 'Not found')}")
        print(f"   Signup Bonus: {card.get('signup_bonus', 'Not found')}")

    # Save results
    scraper.save_results('test_scraped_cards.json')
    print(f"\n💾 Results saved to test_scraped_cards.json")
