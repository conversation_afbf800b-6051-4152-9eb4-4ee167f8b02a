#!/usr/bin/env python3
"""
StackEasy AI Credit Card Recommendation Engine
Build intelligent recommendations based on spending patterns and card features
"""

import pandas as pd
import numpy as np
import json
from collections import defaultdict
import re
from typing import Dict, List, Tuple, Optional

class CreditCardRecommendationEngine:
    def __init__(self, profiles_file: str = 'credit_card_profiles.csv'):
        """Initialize the AI recommendation engine"""
        self.profiles_df = pd.read_csv(profiles_file)
        self.cards_data = self.load_full_card_data()
        
        # Category mappings for spending analysis
        self.spending_categories = {
            'dining': ['dining', 'restaurant', 'food', 'cafe'],
            'travel': ['travel', 'airline', 'flight', 'hotel', 'car rental'],
            'gas': ['gas', 'fuel', 'gasoline', 'ev charging'],
            'groceries': ['grocery', 'groceries', 'supermarket'],
            'entertainment': ['entertainment', 'streaming', 'movies'],
            'shopping': ['shopping', 'retail', 'purchases'],
            'business': ['business', 'office', 'consulting']
        }
        
        print(f"✅ Loaded {len(self.profiles_df)} cards for AI recommendations")
    
    def load_full_card_data(self):
        """Load full card data with benefits and rewards"""
        try:
            import glob
            result_files = glob.glob('scraping_results_*/all_cards_results_*.json')
            latest_file = max(result_files)
            
            with open(latest_file, 'r') as f:
                return json.load(f)
        except:
            print("⚠️ Could not load full card data, using profiles only")
            return []
    
    def get_best_cards_by_category(self, category: str, top_n: int = 3) -> List[Dict]:
        """Find best cards for a specific spending category"""
        category_keywords = self.spending_categories.get(category.lower(), [category.lower()])
        
        scored_cards = []
        
        for _, card in self.profiles_df.iterrows():
            score = 0
            best_for = eval(card['best_for']) if card['best_for'] != '[]' else []
            
            # Check if card is good for this category
            for reward_category in best_for:
                for keyword in category_keywords:
                    if keyword in reward_category.lower():
                        # Extract reward rate
                        rate = self.extract_reward_rate(reward_category)
                        score = max(score, rate)
            
            # Bonus for card type alignment
            if category.lower() == 'travel' and card['card_type'] == 'Travel':
                score += 0.5
            elif category.lower() in ['shopping', 'general'] and card['card_type'] == 'Cashback':
                score += 0.5
            elif category.lower() == 'business' and card['card_type'] == 'Business':
                score += 1.0
            
            # Penalty for high annual fees (unless premium benefits justify it)
            if card['annual_fee'] > 100:
                score -= 0.2
            
            if score > 0:
                scored_cards.append({
                    'card_name': card['card_name'],
                    'issuer': card['issuer'],
                    'score': score,
                    'annual_fee': card['annual_fee'],
                    'card_type': card['card_type'],
                    'total_benefits': card['total_benefits'],
                    'best_for': best_for
                })
        
        # Sort by score and return top N
        scored_cards.sort(key=lambda x: x['score'], reverse=True)
        return scored_cards[:top_n]
    
    def extract_reward_rate(self, reward_text: str) -> float:
        """Extract numeric reward rate from text"""
        if not reward_text:
            return 0.0
        
        # Look for patterns like "4x", "3%", "2 points", etc.
        rate_patterns = [
            r'(\d+(?:\.\d+)?)x',  # 4x points
            r'(\d+(?:\.\d+)?)%',  # 3% cashback
            r'(\d+(?:\.\d+)?)\s*points',  # 2 points
            r'(\d+(?:\.\d+)?)\s*miles',  # 2 miles
        ]
        
        for pattern in rate_patterns:
            match = re.search(pattern, reward_text.lower())
            if match:
                return float(match.group(1))
        
        return 1.0  # Default rate
    
    def analyze_spending_patterns(self, user_spending: Dict[str, float]) -> Dict:
        """Analyze user spending and provide optimization insights"""
        total_spending = sum(user_spending.values())
        
        insights = {
            'total_monthly_spending': total_spending,
            'category_breakdown': {},
            'optimization_opportunities': [],
            'recommended_cards': {}
        }
        
        # Analyze each category
        for category, amount in user_spending.items():
            percentage = (amount / total_spending) * 100
            insights['category_breakdown'][category] = {
                'amount': amount,
                'percentage': percentage
            }
            
            # Find best cards for this category
            best_cards = self.get_best_cards_by_category(category, top_n=1)
            if best_cards:
                insights['recommended_cards'][category] = best_cards[0]
                
                # Calculate potential additional rewards
                current_rate = 1.0  # Assume 1x base rate
                optimal_rate = best_cards[0]['score']
                additional_rewards = amount * (optimal_rate - current_rate) / 100
                
                if additional_rewards > 5:  # Only suggest if meaningful
                    insights['optimization_opportunities'].append({
                        'category': category,
                        'current_monthly_spending': amount,
                        'recommended_card': best_cards[0]['card_name'],
                        'additional_monthly_rewards': additional_rewards,
                        'annual_potential': additional_rewards * 12
                    })
        
        return insights
    
    def recommend_new_cards(self, user_profile: Dict, current_cards: List[str] = None) -> List[Dict]:
        """Recommend new cards based on user profile and spending"""
        if current_cards is None:
            current_cards = []
        
        spending_patterns = user_profile.get('spending_patterns', {})
        credit_score_range = user_profile.get('credit_score_range', 'good')
        
        recommendations = []
        
        # Find gaps in current card portfolio
        for category, amount in spending_patterns.items():
            if amount > 200:  # Only consider significant spending categories
                best_cards = self.get_best_cards_by_category(category, top_n=3)
                
                for card in best_cards:
                    if card['card_name'] not in current_cards:
                        # Calculate potential value
                        annual_spending = amount * 12
                        reward_rate = card['score']
                        annual_rewards = annual_spending * reward_rate / 100
                        net_value = annual_rewards - card['annual_fee']
                        
                        if net_value > 50:  # Only recommend if net positive value
                            match_score = self.calculate_match_score(card, user_profile)
                            
                            recommendations.append({
                                'card_name': card['card_name'],
                                'issuer': card['issuer'],
                                'best_category': category,
                                'annual_fee': card['annual_fee'],
                                'estimated_annual_rewards': annual_rewards,
                                'net_annual_value': net_value,
                                'match_score': match_score,
                                'reason': f"Excellent for {category} spending (${amount}/month)"
                            })
        
        # Sort by match score and net value
        recommendations.sort(key=lambda x: (x['match_score'], x['net_annual_value']), reverse=True)
        return recommendations[:5]  # Top 5 recommendations
    
    def calculate_match_score(self, card: Dict, user_profile: Dict) -> float:
        """Calculate how well a card matches user profile"""
        score = 0.0
        
        # Credit score compatibility
        credit_score = user_profile.get('credit_score_range', 'good')
        if card['annual_fee'] == 0 and credit_score in ['fair', 'building']:
            score += 20
        elif card['annual_fee'] > 300 and credit_score == 'excellent':
            score += 15
        elif card['annual_fee'] < 100 and credit_score == 'good':
            score += 10
        
        # Spending alignment
        spending = user_profile.get('spending_patterns', {})
        total_spending = sum(spending.values())
        
        if total_spending > 2000:  # High spender
            score += card['total_benefits'] * 2
        else:  # Moderate spender
            score += card['total_benefits']
        
        # Card type preference
        preferences = user_profile.get('preferences', [])
        if card['card_type'].lower() in [p.lower() for p in preferences]:
            score += 25
        
        return min(score, 100)  # Cap at 100
    
    def generate_balance_transfer_recommendations(self, current_balances: List[Dict]) -> List[Dict]:
        """Recommend balance transfer opportunities"""
        recommendations = []
        
        # Find cards with 0% intro APR (simplified - would need more data)
        low_fee_cards = self.profiles_df[self.profiles_df['annual_fee'] == 0]
        
        for balance in current_balances:
            current_apr = balance.get('apr', 18.99)
            balance_amount = balance.get('amount', 0)
            
            if current_apr > 15 and balance_amount > 1000:
                # Calculate potential savings
                monthly_interest = (balance_amount * current_apr / 100) / 12
                annual_savings = monthly_interest * 12
                
                # Find suitable transfer cards
                suitable_cards = low_fee_cards[low_fee_cards['card_type'].isin(['General', 'Cashback'])]
                
                if len(suitable_cards) > 0:
                    best_card = suitable_cards.iloc[0]
                    
                    recommendations.append({
                        'current_card': balance.get('card_name', 'Current Card'),
                        'recommended_card': best_card['card_name'],
                        'issuer': best_card['issuer'],
                        'balance_amount': balance_amount,
                        'current_apr': current_apr,
                        'estimated_savings': annual_savings,
                        'transfer_fee': balance_amount * 0.03,  # Assume 3% transfer fee
                        'net_savings': annual_savings - (balance_amount * 0.03)
                    })
        
        return recommendations
    
    def create_personalized_dashboard(self, user_profile: Dict) -> Dict:
        """Create a personalized dashboard like the wireframe"""
        spending = user_profile.get('spending_patterns', {
            'dining': 800,
            'travel': 500,
            'groceries': 400,
            'gas': 200,
            'entertainment': 150
        })
        
        current_cards = user_profile.get('current_cards', [])
        
        dashboard = {
            'spending_insights': self.analyze_spending_patterns(spending),
            'best_cards_by_category': {},
            'new_card_recommendations': self.recommend_new_cards(user_profile, current_cards),
            'optimization_tips': []
        }
        
        # Get best cards for each major category
        major_categories = ['dining', 'travel', 'groceries', 'gas', 'entertainment', 'cashback']
        for category in major_categories:
            dashboard['best_cards_by_category'][category] = self.get_best_cards_by_category(category, top_n=1)
        
        # Generate optimization tips
        insights = dashboard['spending_insights']
        for opp in insights['optimization_opportunities']:
            tip = f"💡 Use {opp['recommended_card']} for {opp['category']} to earn ${opp['additional_monthly_rewards']:.0f} more per month"
            dashboard['optimization_tips'].append(tip)
        
        return dashboard

def main():
    """Demo the AI recommendation engine"""
    print("🤖 STACKEASY AI RECOMMENDATION ENGINE")
    print("🎯 Building intelligent credit card recommendations")
    print("=" * 60)
    
    # Initialize the engine
    engine = CreditCardRecommendationEngine()
    
    # Example user profile (like Alex Johnson from wireframe)
    user_profile = {
        'spending_patterns': {
            'dining': 800,      # 30% of spending on dining (like wireframe)
            'travel': 600,      # Summer vacation planned
            'groceries': 400,
            'gas': 200,
            'entertainment': 150,
            'shopping': 300
        },
        'credit_score_range': 'excellent',
        'preferences': ['travel', 'cashback'],
        'current_cards': ['Chase Sapphire', 'Citi Double Cash']
    }
    
    print(f"👤 Analyzing profile for user with ${sum(user_profile['spending_patterns'].values())}/month spending")
    
    # Generate personalized dashboard
    dashboard = engine.create_personalized_dashboard(user_profile)
    
    # Display results like the wireframe
    print(f"\n🎯 SPENDING INSIGHTS:")
    for category, data in dashboard['spending_insights']['category_breakdown'].items():
        print(f"   {category.title()}: ${data['amount']}/month ({data['percentage']:.1f}%)")
    
    print(f"\n🏆 BEST CARDS BY CATEGORY:")
    for category, cards in dashboard['best_cards_by_category'].items():
        if cards:
            card = cards[0]
            print(f"   {category.title()}: {card['card_name']} ({card['issuer']}) - Score: {card['score']:.1f}")
    
    print(f"\n💡 OPTIMIZATION TIPS:")
    for tip in dashboard['optimization_tips']:
        print(f"   {tip}")
    
    print(f"\n🆕 NEW CARD RECOMMENDATIONS:")
    for i, rec in enumerate(dashboard['new_card_recommendations'][:3], 1):
        print(f"   {i}. {rec['card_name']} ({rec['issuer']})")
        print(f"      Best for: {rec['best_category']} | Net Value: ${rec['net_annual_value']:.0f}/year")
        print(f"      Match Score: {rec['match_score']:.0f}% | {rec['reason']}")
    
    # Save results
    with open('ai_recommendations_demo.json', 'w') as f:
        json.dump(dashboard, f, indent=2)
    
    print(f"\n✅ AI RECOMMENDATIONS COMPLETE!")
    print(f"📁 Saved demo results to: ai_recommendations_demo.json")
    print(f"🎉 Ready to integrate with StackEasy frontend!")

if __name__ == "__main__":
    main()
